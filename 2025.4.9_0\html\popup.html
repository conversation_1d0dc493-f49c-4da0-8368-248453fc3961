<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>求职方舟</title>
    <link rel="stylesheet" href="../css/popup.css">
</head>
<body>
    <div class="profile">
        <img src="../image/icon128.png" alt="Profile" class="profile-img">
        <span class="profile-name">求职方舟</span>
    </div>
    <div class="section">
        <h2>面板</h2>
        <div class="options-container">
            <div class="option">
                <span>方舟 悬浮按钮</span>
                <div class="radio-group">
                    <label class="radio-option">
                        <input type="radio" name="arcButtonMode" value="show" id="arcButtonShow">
                        <span class="radio-label">常驻</span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="arcButtonMode" value="hidden" id="arcButtonHidden">
                        <span class="radio-label">隐藏</span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="arcButtonMode" value="auto" id="arcButtonAuto" checked>
                        <span class="radio-label">智能</span>
                    </label>
                </div>
            </div>
            <div class="option">
                <span>表单高亮</span>
                <label class="switch">
                    <input id="highlightButton" type="checkbox" checked>
                    <span class="slider round"></span>
                </label>
            </div>
            <!-- <div class="option">
                <span>对话快捷键</span>
                <span class="shortcut">⌘K</span>
            </div> -->
        </div>
    </div>
    <div class="section">
        <h2>填写</h2>
        <div class="options-container">
            <div class="option">
                <span>生成专岗美化简历</span>
                <label class="switch">
                    <input id="beautifyButton" type="checkbox" checked>
                    <span class="slider round"></span>
                </label>
            </div>
            <div class="option">
                <span>智能学习简历</span>
                <label class="switch">
                    <input id="learningButton" type="checkbox" checked>
                    <span class="slider round"></span>
                </label>
            </div>
            <!-- <div class="option">
                <span>探索快捷键（支持划线与截图）</span>
                <span class="shortcut">⌘J</span>
            </div>
            <div class="option">
                <span>保留文字下划线</span>
                <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider round"></span>
                </label>
            </div> -->
        </div>
    </div>
    <div class="section">
        <h2>战绩</h2>
        <div class="options-container">
            <div class="option">
                <span>填写招聘网站</span>
                <span id="websiteCount" class="shortcut">0</span>
            </div>
            <div class="option">
                <span>填写条目</span>
                <span id="fieldCount" class="shortcut">0</span>
            </div>
            <!-- <div class="option">
                <span>保留文字下划线</span>
                <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider round"></span>
                </label>
            </div> -->
        </div>
    </div>
    <div class="footer">
        <button id="visitArc">访问 求职方舟</button>
        <button id="logout">登出</button>
    </div>
    <script type="module" src="../js/popup.js"></script>
</body>
</html>
