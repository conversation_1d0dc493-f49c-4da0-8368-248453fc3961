let shadowRoot=null,logoButtun=null,resumeWindow=null,windowData=null,simplemde=null;async function initResumeInterface(){try{await createArcAiInterface(),loadCSS(),loadFonts(),initHighlight(),await bindInteractions(logoButtun,resumeWindow),initSimpleMDE(),initAdjustWindowHeight()}catch(e){}}async function createArcAiInterface(){try{const e=document.createElement("div");if(e.id="ark-ai",shadowRoot=e.attachShadow({mode:"open"}),document.body.appendChild(e),await initArcButtonDisplay(),logoButtun=createFloatingButton(),!logoButtun)return;if(resumeWindow=await createResumeWindow(),!resumeWindow)return}catch(e){}}async function initArcButtonDisplay(){try{const{arcButtonMode:e="auto"}=await chrome.storage.local.get(["arcButtonMode"]);toggleArcButtonShow(e)}catch(e){}}function toggleArcButtonShow(e){const t=document.getElementById("ark-ai");if(t)if("boolean"!=typeof e)switch(e){case"show":t.style.display="block";break;case"auto":break;case"hidden":t.style.display="none"}else t.style.display=e?"block":"none"}function createFloatingButton(){const e=document.createElement("button");e.id="logo-button";const t=document.createElement("img");return t.src=chrome.runtime.getURL("image/icon128.png"),e.appendChild(t),shadowRoot.appendChild(e),e}async function createResumeWindow(){const e=document.createElement("div");e.id="resume-window-container";try{const t=await fetch(chrome.runtime.getURL("html/resumeWindow.html")),n=await t.text();e.innerHTML=n}catch(e){return null}shadowRoot.appendChild(e);const t=e.querySelector("#resume-window");if(!t)return null;return e.querySelector("#header-logo").src=chrome.runtime.getURL("image/icon128.png"),t}async function initHighlight(){try{const e=!1!==(await chrome.storage.local.get(["highlightEnabled"])).highlightEnabled;document.documentElement.style.setProperty("--highlight-enabled",e?"1":"0")}catch(e){document.documentElement.style.setProperty("--highlight-enabled","1")}}async function closeHighlight(){await waitTime(1e3),document.documentElement.style.setProperty("--highlight-enabled","0")}function loadCSS(){const e=document.createElement("link");e.rel="stylesheet",e.href=chrome.runtime.getURL("css/lib/font-awesome.all.min.css"),shadowRoot.appendChild(e);const t=document.createElement("link");t.rel="stylesheet",t.href=chrome.runtime.getURL("css/lib/simplemde.min.css"),shadowRoot.appendChild(t);const n=document.createElement("link");n.rel="stylesheet",n.href=chrome.runtime.getURL("css/resumeInterface.css"),shadowRoot.appendChild(n)}function loadFonts(){const e=chrome.runtime.getURL("webfonts/fa-solid-900.woff2");new FontFace("Font Awesome 6 Free",`url(${e})`,{weight:"900"}).load().then((e=>{document.fonts.add(e)})).catch((e=>{}))}async function bindInteractions(e,t){if(!t)return;e.addEventListener("click",(async()=>{const{auth:e}=await chrome.storage.local.get(["auth"]);e?toggleResumeWindowHidden(!0):confirm("尚未登录到求职方舟，是否立即前往登录？")&&window.open(`${window.config.LOGIN_URL}`,"_blank")}));t.querySelector("#close-window").addEventListener("click",(()=>{toggleResumeWindowHidden(!1)}));t.querySelector("#start-button").addEventListener("click",(()=>{clickStartButton()}));t.querySelector(".resume-header").addEventListener("click",(()=>{toggleResumeWindowMinimize()}));t.querySelector("#state-text").addEventListener("click",(()=>{toggleResumeWindowMinimize()}));t.querySelector("#resume-version").addEventListener("change",(e=>{changeVersionSelect(e)}));const n=t.querySelector("#beautify-checkbox"),o=await chrome.storage.local.get(["beautifyResume"]);n.checked=!0===o.beautifyResume,n.addEventListener("change",(e=>{changeBeautifyCheckbox(e.target.checked),chrome.storage.local.set({beautifyResume:e.target.checked})})),bindQuickLinkButtons()}function bindWindowData(e){const t=resumeWindow.querySelector("#company-name"),n=resumeWindow.querySelector("#position-name");t.value=e.company||"",n.value=e.position||"",window.campusSource&&window.campusSource.company&&(t.value=window.campusSource.company);const o=resumeWindow.querySelector("#resume-version");if(o){let t="";for(const n of e.resumeList)t+=`<option value="${n.id}">${n.title}</option>`;o.innerHTML=t,o.value=e.resumeId}for(const t of e.resumeList)if(t.id===e.resumeId){setResumeContent(t.resumeMd);break}}async function toggleResumeWindowHidden(e){if(resumeWindow)if(e){if(resumeWindow.classList.add("min"),resumeWindow.style.display="flex",resumeWindow.style.opacity="0",await waitTime(10),resumeWindow.style.opacity="1",await waitTime(200),logoButtun&&(logoButtun.style.display="none"),"init"===simpleMDELoadedState)return window.setStateText("初始化中...","min"),void setTimeout((()=>{toggleResumeWindowMinimize("show")}),1e3);if("error"===simpleMDELoadedState)return void window.setStateText("初始化失败！我不行了，靠你咯...","min");windowData?isStartReady()?window.setStateText("方舟已就位，等待启动","show"):(window.setStateText("方舟准备中，请先填写公司和职位","show"),await waitTime(600),resumeWindow.querySelector("#company-name").focus()):(window.setStateText("加载简历...","min"),windowData=await initResumeFromServer(),"error"==windowData.status?(windowData.detail&&"用户没有简历"===windowData.detail?(await toggleResumeWindowHidden(!1),confirm("你还没有简历，快前往【求职方舟】上传简历吧！")&&window.open(`${window.config.WEB_URL}`,"_blank")):window.setStateText("糟糕！简历请求失败，刷新再试试","min"),windowData=null):windowData?(bindWindowData(windowData),isStartReady()?window.setStateText("方舟已就位，等待启动","show"):(window.setStateText("方舟准备中，请先填写公司和职位","show"),await waitTime(600),resumeWindow.querySelector("#company-name").focus())):window.setStateText("糟糕！简历加载失败，刷新再试试","min"))}else logoButtun&&(logoButtun.style.display="block"),await waitTime(10),resumeWindow.style.opacity="0",await waitTime(200),resumeWindow.style.display="none",resumeWindow.classList.add("min")}async function toggleResumeWindowMinimize(e=null){const t=resumeWindow.querySelector(".resume-display");null===e&&(e=resumeWindow.classList.contains("min")?"show":"min"),"show"===e&&resumeWindow.classList.contains("min")?(resumeWindow.classList.remove("min"),t.style.display="block",await waitTime(100),t.style.opacity="1"):"min"!==e||resumeWindow.classList.contains("min")||(t.style.opacity="0",await waitTime(200),t.style.display="none",resumeWindow.classList.add("min"))}let runningState="ready";function isRunning(){return"running"===runningState}function clickStartButton(){if("success"===simpleMDELoadedState){if(!isStartReady())return alert("要先填写公司和职位，才能生成专岗美化简历哦！"),window.setStateText("方舟准备中，请先填写公司和职位","show"),void resumeWindow.querySelector("#company-name").focus();if(["ready","success","error"].includes(runningState)){initHighlight(!0),changeStartButtonState("running");const e=resumeWindow.querySelector("#company-name").value,t=resumeWindow.querySelector("#position-name").value,n=getResumeContent(),o=windowData.resumeId;window.runFillResume(e,t,n,o,isNeedBeautify(),(e=>{"success"==e.status?changeStartButtonState("success"):changeStartButtonState("error")}))}else"running"==runningState?changeStartButtonState("pause"):"pause"==runningState&&changeStartButtonState("running")}}function changeStartButtonState(e){runningState=e;const t=resumeWindow.querySelector("#start-button");"running"==e?(t.innerHTML='<i class="fas fa-pause"></i>',t.classList.add("paused")):"pause"==e?(t.innerHTML='<i class="fas fa-play"></i>',t.classList.remove("paused")):"success"==e?(t.innerHTML='<i class="fas fa-calendar-check"></i>',t.classList.remove("paused")):"error"==e?(t.innerHTML='<i class="fas fa-bug"></i>',t.classList.remove("paused")):"learning"==e&&(t.innerHTML='<i class="fas fa-wand-magic-sparkles"></i>')}async function changeVersionSelect(e){const t=e.target.value;let n;windowData.resumeId=t,setResumeContent(""),window.setStateText("获取简历中...");for(const e of windowData.resumeList)if(Number(e.id)===Number(t)){n=e;break}if(n){if(n.resumeMd)setResumeContent(n.resumeMd);else{const e=await getResumeMdFromServer(t);if(!e.resumeMd)return void window.setStateText("糟糕！简历不存在");setResumeContent(e.resumeMd)}isStartReady()?window.setStateText("方舟已就位，等待启动","show"):(window.setStateText("方舟准备中，请先填写公司和职位","show"),resumeWindow.querySelector("#company-name").focus())}else window.setStateText("未找到对应的简历")}function changeBeautifyCheckbox(e){isStartReady()?window.setStateText("方舟已就位，等待启动","show"):(window.setStateText("方舟准备中，请先填写公司和职位","show"),resumeWindow.querySelector("#company-name").focus())}function isStartReady(){if(isNeedBeautify()){const e=resumeWindow.querySelector("#company-name"),t=resumeWindow.querySelector("#position-name");return e.value&&t.value}return!0}function isNeedBeautify(){return resumeWindow.querySelector("#beautify-checkbox").checked}function bindBeautifyResume(e){const{isNew:t,resumeItem:n,resumeId:o,resumeMd:i}=e;if(t){n.resumeMd=i,windowData.resumeList.unshift(n),windowData.resumeId=o,windowData.company=resumeWindow.querySelector("#company-name").value||"",windowData.position=resumeWindow.querySelector("#position-name").value||"";const e=resumeWindow.querySelector("#resume-version");e.innerHTML="";let t="";for(const e of windowData.resumeList)t+=`<option value="${e.id}">${e.title}</option>`;e.innerHTML=t,typeResumeContent(i)}else typeResumeContent(i)}async function breatheJobInfo(){const e=resumeWindow.querySelector("#company-name"),t=resumeWindow.querySelector("#position-name");e.classList.remove("normal-bg"),t.classList.remove("normal-bg"),e.classList.add("breathing-bg"),t.classList.add("breathing-bg"),await waitTime(5e3),e.classList.remove("breathing-bg"),t.classList.remove("breathing-bg"),e.classList.add("normal-bg"),t.classList.add("normal-bg")}async function breatheResume(e){const t=shadowRoot.querySelector(".CodeMirror");"begin"===e?(t.classList.remove("normal-bg"),t.classList.add("breathing-bg")):"end"===e&&(t.classList.remove("breathing-bg"),t.classList.add("normal-bg"))}let stateTextTypeTimeout=null;function setStateText(e,t=null){const n=resumeWindow.querySelector("#state-text");if(n.textContent!==e){stateTextTypeTimeout&&clearTimeout(stateTextTypeTimeout);let t=0;n.textContent="";const o=()=>{t<e.length?(n.textContent+=e.charAt(t),t++,stateTextTypeTimeout=setTimeout(o,500/e.length)):stateTextTypeTimeout=null};o()}t&&toggleResumeWindowMinimize(t)}function initAdjustWindowHeight(){adjustWindowHeight(),window.addEventListener("resize",debounce(adjustWindowHeight,200))}function debounce(e,t){let n;return function(...o){clearTimeout(n),n=setTimeout((()=>{clearTimeout(n),e(...o)}),t)}}function adjustWindowHeight(){if(!resumeWindow)return;const e=window.innerHeight-80;let t=700;t>e&&(t=Math.max(e,600));const n=shadowRoot.querySelector(".CodeMirror"),o=n.classList.contains("showNewVersion")?350:310;n.style.setProperty("height",t-o+"px","important")}let simpleMDELoadedState="init";function initSimpleMDE(){if("undefined"==typeof SimpleMDE)return void(simpleMDELoadedState="error");const e=shadowRoot.querySelector("#resume-editor");if(e)try{simplemde=new SimpleMDE({autosave:{enabled:!0,uniqueId:"resume",delay:1e3},element:e,forceSync:!0,initialValue:"",placeholder:"请将简历粘贴到这里，建议使用Markdown格式",spellChecker:!1,status:["autosave","lines","words"],toolbar:!1,autoDownloadFontAwesome:!1}),shadowRoot.querySelector(".CodeMirror").classList.add("normal-bg");let t="";simplemde.codemirror.on("change",(()=>{})),simplemde.codemirror.on("focus",(()=>{t=simplemde.value()})),simplemde.codemirror.on("blur",(()=>{t!==simplemde.value()&&saveResumeContent(simplemde.value())})),simpleMDELoadedState="success"}catch(e){simpleMDELoadedState="error"}else simpleMDELoadedState="error"}async function saveResumeContent(e){for(let t of windowData.resumeList)if(t.id===windowData.resumeId){t.resumeMd=e;break}await saveResumeMdToServer(windowData.resumeId,e)}function getResumeContent(){return resumeWindow.querySelector("#resume-editor").value}function setResumeContent(e){const t=()=>{null===simplemde?setTimeout(t,100):simplemde.codemirror.setValue(e)};t()}let currentTypewriterTimeout=null;function typeResumeContent(e){currentTypewriterTimeout&&clearTimeout(currentTypewriterTimeout);let t=0;simplemde.codemirror.setValue("");const n=()=>{if(t<e.length){const o=simplemde.codemirror.getDoc(),i=o.lastLine(),s={line:i,ch:o.getLine(i)?.length||0};if(o.replaceRange(e.charAt(t),s),"\n"===e.charAt(t)){simplemde.codemirror.getScrollerElement().querySelector(".CodeMirror-sizer").scrollIntoView({block:"end"})}t++,currentTypewriterTimeout=setTimeout(n,Math.floor(1e4/e.length))}else{currentTypewriterTimeout=null;simplemde.codemirror.getScrollerElement().querySelector(".CodeMirror-sizer").scrollIntoView({block:"start"})}};n()}function stopTypeResumeContent(e){currentTypewriterTimeout&&(clearTimeout(currentTypewriterTimeout),simplemde.codemirror.setValue(e))}async function initResumeFromServer(){const e={url:window.location.href};return await httpRequest("initResume",e)}async function getResumeMdFromServer(e){const t={resumeId:e};return await httpRequest("getResumeMd",t)}async function saveResumeMdToServer(e,t){const n={resumeId:e,resumeMd:t};return await httpRequest("saveResumeMd",n)}async function httpRequest(e,t){try{return await fetchWithJwt(`${window.config.API_BASE_URL}${e}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})}catch(e){return{status:"error",detail:e.message||"未知错误"}}}function toggleHighlight(e){document.documentElement.style.setProperty("--highlight-enabled",e?"1":"0")}function toggleBeautifyResume(e){const t=resumeWindow.querySelector("#beautify-checkbox");(e&&!t.checked||!e&&t.checked)&&t.click()}function bindQuickLinkButtons(){const e=resumeWindow.querySelector("#history-table-btn"),t=resumeWindow.querySelector("#campus-table-btn"),n=resumeWindow.querySelector("#my-resume-btn");e&&e.addEventListener("click",(()=>{window.open(window.config.HISTORY_URL,"_blank")})),t&&t.addEventListener("click",(()=>{window.open(window.config.CAMPUS_URL,"_blank")})),n&&n.addEventListener("click",(()=>{window.open(window.config.WEB_URL,"_blank")}))}function isQiuzhiFangzhou(){const e=new URL(window.location.href);return!!window.config.ALL_WEB_URLS.some((t=>e.href.startsWith(t)))}function bindTabSource(){const e=new URL(window.location.href);window.config.CAMPUS_URL&&e.href.startsWith(window.config.CAMPUS_URL)&&document.addEventListener("click",(e=>{const t=e.target.closest("a");t?.dataset.campusId&&t?.dataset.company&&chrome.runtime.sendMessage({type:"clickSource",source:{campusId:t.dataset.campusId,company:t.dataset.company}},(e=>{}))}),!0)}function initTabSource(){chrome.runtime.sendMessage({type:"getSource"},(e=>{e?.source&&(window.campusSource=e.source)}))}function initHistoryCampusId(){const e=new URL(window.location.href);window.config.CAMPUS_URL&&e.href.startsWith(window.config.CAMPUS_URL)&&chrome.runtime.onMessage.addListener(((e,t,n)=>{"addHistoryCampusId"===e.type&&window.postMessage({type:"addHistoryCampusId",data:e.data},"*")}))}chrome.runtime.onMessage.addListener(((e,t,n)=>"toggleArcButtonMode"===e.action?(toggleArcButtonShow(e.state),n({success:!0}),!0):"toggleHighlight"===e.action?(toggleHighlight(e.state),n({success:!0}),!0):"toggleBeautifyResume"===e.action?(toggleBeautifyResume(e.state),n({success:!0}),!0):void 0)),window.setStateText=setStateText,window.isRunning=isRunning,window.bindBeautifyResume=bindBeautifyResume,window.stopTypeResumeContent=stopTypeResumeContent,window.breatheJobInfo=breatheJobInfo,window.changeStartButtonState=changeStartButtonState,window.closeHighlight=closeHighlight,window.campusSource=null;let checkCount=0;const checkInterval=2e3;function checkResumeText(){return new Promise((e=>{const t=["https://xyz.51job.com/External/MyResume/FillInResume.aspx","https://xiaoyuan.zhaopin.com/scrd/resume2"],n=new URL(window.location.href);for(const o of t)if(n.href.startsWith(o))return void e(!0);const o=async()=>{const{arcButtonMode:t="auto"}=await chrome.storage.local.get(["arcButtonMode"]);if("show"===t)return clearInterval(i),void e(!0);const n=document.body.innerText;/(?:^|[^\u4e00-\u9fa5])(简历|姓名)|(简历|姓名)(?:[^\u4e00-\u9fa5]|$)/.test(n)&&(clearInterval(i),e(!0)),checkCount++},i=setInterval(o,2e3);o()}))}async function checkForNewVersion(){try{const e=chrome.runtime.getManifest().version,{lastVersionCheck:t,latestVersion:n}=await chrome.storage.local.get(["lastVersionCheck","latestVersion"]),o=Date.now();let i;if(t&&n&&o-t<36e5)i=n;else try{const e=`${window.config.VERSION_URL}?t=${Date.now()}`,t=await fetch(e);if(!t.ok)throw new Error(`获取版本信息失败: ${t.status}`);i=(await t.text()).trim(),await chrome.storage.local.set({lastVersionCheck:o,latestVersion:i})}catch(e){if(!n)throw e;i=n}isNewerVersion(i,e)&&showVersionNotification()}catch(e){}}function isNewerVersion(e,t){const n=e.split(".").map(Number),o=t.split(".").map(Number);for(let e=0;e<Math.max(n.length,o.length);e++){const t=n[e]||0,i=o[e]||0;if(t>i)return!0;if(t<i)return!1}return!1}function showVersionNotification(){const e=resumeWindow.querySelector("#version-notification");if(e){e.style.display="block";const t=resumeWindow.querySelector(".CodeMirror");t&&(t.classList.add("showNewVersion"),adjustWindowHeight());const n=e.querySelector("#update-now-btn");n&&n.addEventListener("click",(()=>{window.open(`${window.config.AUTOFILL_URL}?newversion=true`,"_blank")}))}}function initVersionCheck(){navigator.userAgent.includes("Edg/")||setTimeout((()=>{checkForNewVersion()}),3e3)}(async()=>{if(window.config||await new Promise((e=>{Object.defineProperty(window,"config",{set(t){delete window.config,window.config=t,e()},configurable:!0})})),isQiuzhiFangzhou())return bindTabSource(),void initHistoryCampusId();await checkResumeText()&&(initTabSource(),await initResumeInterface(),initVersionCheck())})();