{"field_mappings": {"未知字段": [{"user_value": "", "field_info": {"type": "input", "label": "", "name": "", "placeholder": "", "id": "", "class": "sd-Input-input-1WaF0 sd-Input-common-input-2VyCG sd-Input-has-addon-1HzG3", "current_value": "", "original_value": ""}, "frequency": 26, "last_used": "2025-05-30T11:12:57.383097"}], "请输入手机号": [{"user_value": "18560613556", "field_info": {"type": "input", "label": "", "name": "", "placeholder": "请输入手机号", "id": "", "class": "sd-Input-input-1WaF0 sd-Input-common-input-2VyCG", "current_value": "18560613556", "original_value": "18560613556"}, "frequency": 1, "last_used": "2025-05-30T11:12:57.383097"}], "个人微信号（海外留学生建议填写）": [{"user_value": "l18560613556", "field_info": {"type": "input", "label": "", "name": "", "placeholder": "个人微信号（海外留学生建议填写）", "id": "", "class": "sd-Input-input-1WaF0 sd-Input-common-input-2VyCG", "current_value": "l18560613556", "original_value": "l18560613556"}, "frequency": 1, "last_used": "2025-05-30T11:12:57.383097"}]}, "website_patterns": {}, "user_preferences": {"input": {"": 23, "18560613556": 1, "l18560613556": 1}, "custom_select": {"": 3}}, "fill_history": [{"timestamp": "2025-05-30T11:12:57.383097", "url": "https://app.mokahr.com/campus_apply/didiglobal/96064?recommendCode=DSKnCd9b#/job/574c61c4-bf39-442d-9fa9-1b00fe0b48c3/apply", "title": "滴滴 - 校园招聘", "corrections_count": 28, "total_fields": 31}]}