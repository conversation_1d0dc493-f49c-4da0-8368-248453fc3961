const ENV_CONFIG={development:{WEB:{HOST:"http://localhost",PORT:"5173"},API:{HOST:"http://localhost",PORT:"8080"}},production:{WEB:{HOST:"https://www.qiuzhifangzhou.com",PORT:"443"},API:{HOST:"https://api.qiuzhifangzhou.com",PORT:"443"}}},ENV="production",CONFIG=ENV_CONFIG[ENV],buildUrl=(t,o)=>{const{HOST:r,PORT:l}=CONFIG[o];return`${r}${"443"===l?"":`:${l}`}${t}`};export const API_BASE_URL=buildUrl("/api/chrome/","API");export const API_AUTH_URL=buildUrl("/api/auth/","API");export const API_HISTORY_URL=buildUrl("/api/history/","API");export const WEB_DOMAIN=CONFIG.WEB.HOST;export const WEB_URL=buildUrl("/resume","WEB");export const LOGIN_URL=buildUrl("/resume?login=true","WEB");export const CAMPUS_URL=buildUrl("/campus","WEB");export const HISTORY_URL=buildUrl("/history","WEB");export const AUTOFILL_URL=buildUrl("/autofill","WEB");export const VERSION_URL=buildUrl("/crx/version.txt","WEB");export const ALL_WEB_URLS=["http://localhost:5173","https://www.qiuzhifangzhou.com"];export const isDevEnv=()=>!1;export const getEnv=()=>ENV;