"""
表单填写器 - 整合视觉AI和网页自动化
"""

import os
import time
import logging
from typing import Dict, List, Any, Optional
import json
from datetime import datetime
from selenium.webdriver.common.by import By

from resume_parser import ResumeParser
from vision_api import VisionAPI
from web_automation import WebAutomation
from learning_module import LearningModule
from config import SYSTEM_CONFIG, RESUME_PATH

logger = logging.getLogger(__name__)


class FormFiller:
    """表单自动填写器"""

    def __init__(self):
        self.resume_parser = ResumeParser(RESUME_PATH)
        self.vision_api = VisionAPI()
        self.web_automation = WebAutomation(
            browser=SYSTEM_CONFIG['browser'],
            headless=SYSTEM_CONFIG['headless']
        )
        self.learning_module = LearningModule()
        self.resume_data = None
        self.form_elements = []
        self.form_analysis = {}  # 存储表单分析结果
        self.original_suggestions = {}  # 保存原始AI建议

    def initialize(self):
        """初始化"""
        logger.info("开始初始化自动填写工具...")

        # 解析简历
        self.resume_data = self.resume_parser.parse()
        logger.info("简历解析完成")
        logger.info(f"解析结果摘要:\n{self.resume_parser.get_summary()}")

        # 显示学习统计
        stats = self.learning_module.get_learning_stats()
        if stats['total_fill_sessions'] > 0:
            logger.info(f"学习统计: 已学习{stats['learned_fields']}个字段, "
                       f"总计{stats['total_fill_sessions']}次填写会话")

        # 初始化浏览器
        self.web_automation.init_browser()
        logger.info("浏览器初始化完成")

    def run(self):
        """运行自动填写流程"""
        try:
            # 初始化
            self.initialize()

            # 等待用户登录并进入填写页面
            self.web_automation.wait_for_user_ready()

            # 分析页面
            self.analyze_page()

            # 填写表单
            self.fill_form()

            # 填写完成，等待用户修改
            self.wait_for_user_corrections()

        except Exception as e:
            logger.error(f"自动填写过程出错: {e}")
            raise
        finally:
            self.web_automation.close()

    def analyze_page(self):
        """分析页面"""
        logger.info("开始分析页面...")

        # 截图整个页面
        screenshot_path = self.web_automation.take_full_page_screenshot(
            f"full_page_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        )

        # 获取HTML表单元素
        logger.info("分析HTML表单元素...")
        self.form_elements = self.web_automation.get_form_elements()
        logger.info(f"找到{len(self.form_elements)}个表单元素")

        # 提取所有下拉菜单的选项
        logger.info("提取下拉菜单选项...")
        self._extract_all_dropdown_options()

        # 使用视觉AI分析页面（可选，作为补充）
        logger.info("使用视觉AI分析页面...")
        try:
            visual_fields = self.vision_api.detect_form_fields(screenshot_path)
            logger.info(f"视觉AI识别结果: {json.dumps(visual_fields, ensure_ascii=False, indent=2)}")
        except Exception as e:
            logger.warning(f"视觉AI分析失败: {e}")
            visual_fields = {}

        # 构建完整的表单分析结果
        self.form_analysis = self._build_form_analysis()

        # 打印分析结果
        self._print_analysis_summary()

    def _extract_all_dropdown_options(self):
        """提取所有下拉菜单的选项"""
        for i, element_info in enumerate(self.form_elements):
            if element_info['type'] in ['select', 'custom_select']:
                field_identifier = self._get_field_identifier(element_info)
                logger.info(f"提取下拉菜单选项: {field_identifier}")

                element = element_info['element']
                options = element_info.get('options', [])

                # 如果是自定义下拉菜单且没有预加载的选项，尝试提取
                if element_info['type'] == 'custom_select' and not options:
                    try:
                        dynamic_info = self.web_automation.extract_dynamic_content(element)
                        options = dynamic_info.get('options', [])
                        element_info['options'] = options
                    except Exception as e:
                        logger.warning(f"提取自定义下拉菜单选项失败: {e}")

                if options:
                    logger.info(f"  找到{len(options)}个选项: {options[:10]}{'...' if len(options) > 10 else ''}")
                else:
                    logger.warning(f"  未找到选项")

    def _build_form_analysis(self) -> Dict:
        """构建完整的表单分析结果"""
        analysis = {
            'text_fields': [],      # 文本输入字段
            'dropdown_fields': [],  # 下拉菜单字段
            'other_fields': []      # 其他类型字段
        }

        for element_info in self.form_elements:
            field_data = {
                'identifier': self._get_field_identifier(element_info),
                'type': element_info['type'],
                'element_info': element_info,
                'label': element_info.get('label', ''),
                'placeholder': element_info.get('placeholder', ''),
                'name': element_info.get('name', ''),
                'required': element_info.get('required', False)
            }

            if element_info['type'] in ['input', 'textarea']:
                field_data['input_type'] = element_info.get('input_type', 'text')
                analysis['text_fields'].append(field_data)
            elif element_info['type'] in ['select', 'custom_select']:
                field_data['options'] = element_info.get('options', [])
                analysis['dropdown_fields'].append(field_data)
            else:
                analysis['other_fields'].append(field_data)

        return analysis

    def _print_analysis_summary(self):
        """打印分析摘要"""
        logger.info("="*60)
        logger.info("页面表单分析摘要:")
        logger.info(f"文本字段: {len(self.form_analysis['text_fields'])}个")
        logger.info(f"下拉菜单: {len(self.form_analysis['dropdown_fields'])}个")
        logger.info(f"其他字段: {len(self.form_analysis['other_fields'])}个")

        # 打印下拉菜单详情
        for field in self.form_analysis['dropdown_fields']:
            options_count = len(field.get('options', []))
            logger.info(f"  下拉菜单 '{field['identifier']}': {options_count}个选项")

        logger.info("="*60)

    def fill_form(self):
        """填写表单"""
        logger.info("开始填写表单...")

        # 分别处理不同类型的字段
        self._fill_text_fields()
        self._fill_dropdown_fields()
        self._fill_other_fields()

    def _fill_text_fields(self):
        """填写文本字段"""
        logger.info(f"开始填写{len(self.form_analysis['text_fields'])}个文本字段...")

        for field_data in self.form_analysis['text_fields']:
            try:
                logger.info(f"处理文本字段: {field_data['identifier']}")
                self._fill_single_text_field(field_data)
                time.sleep(0.5)
            except Exception as e:
                logger.error(f"填写文本字段失败: {e}")
                continue

    def _fill_dropdown_fields(self):
        """填写下拉菜单字段"""
        logger.info(f"开始填写{len(self.form_analysis['dropdown_fields'])}个下拉菜单...")

        for field_data in self.form_analysis['dropdown_fields']:
            try:
                logger.info(f"处理下拉菜单: {field_data['identifier']}")
                self._fill_single_dropdown_field(field_data)
                time.sleep(0.5)
            except Exception as e:
                logger.error(f"填写下拉菜单失败: {e}")
                continue

    def _fill_other_fields(self):
        """填写其他类型字段"""
        if self.form_analysis['other_fields']:
            logger.info(f"处理{len(self.form_analysis['other_fields'])}个其他类型字段...")
            # 这里可以根据需要添加特殊字段的处理逻辑

        # 处理动态表单部分（如多段教育经历）
        self._handle_dynamic_forms()

    def _handle_dynamic_forms(self):
        """处理动态表单，如多段教育经历"""
        try:
            # 检查是否有多段教育经历需要添加
            if len(self.resume_data['education']) > 1:
                logger.info(f"检测到{len(self.resume_data['education'])}段教育经历，需要添加动态表单")

                # 查找添加按钮
                add_buttons = self._find_add_buttons()

                # 为每个额外的教育经历添加表单
                for i in range(1, len(self.resume_data['education'])):
                    logger.info(f"添加第{i+1}段教育经历")

                    # 查找教育相关的添加按钮
                    education_add_button = self._find_education_add_button(add_buttons)
                    if education_add_button:
                        logger.info("找到教育经历添加按钮，点击添加")
                        self.web_automation.click_element(education_add_button)
                        time.sleep(2)  # 等待新表单加载

                        # 重新分析页面，获取新增的表单字段
                        self._update_form_analysis_after_add()

                        # 填写新增的教育经历
                        self._fill_additional_education(i)
                    else:
                        logger.warning("未找到教育经历添加按钮")
                        break

        except Exception as e:
            logger.error(f"处理动态表单失败: {e}")

    def _find_add_buttons(self):
        """查找页面中的添加按钮"""
        add_buttons = []

        # 常见的添加按钮选择器
        add_selectors = [
            'button:contains("添加")',
            'button:contains("新增")',
            'button:contains("+")',
            'a:contains("添加")',
            'a:contains("新增")',
            '.add-btn',
            '.add-button',
            '[data-action="add"]',
            '.btn-add'
        ]

        # 由于CSS选择器不支持:contains，使用XPath
        xpath_selectors = [
            '//button[contains(text(), "添加")]',
            '//button[contains(text(), "新增")]',
            '//button[contains(text(), "+")]',
            '//a[contains(text(), "添加")]',
            '//a[contains(text(), "新增")]',
            '//span[contains(text(), "添加")]/..',
            '//span[contains(text(), "新增")]/..'
        ]

        try:
            # 使用CSS选择器查找
            for selector in add_selectors:
                if not selector.startswith('button:contains') and not selector.startswith('a:contains'):
                    elements = self.web_automation.driver.find_elements(By.CSS_SELECTOR, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            add_buttons.append(elem)

            # 使用XPath查找
            for xpath in xpath_selectors:
                elements = self.web_automation.driver.find_elements(By.XPATH, xpath)
                for elem in elements:
                    if elem.is_displayed() and elem not in add_buttons:
                        add_buttons.append(elem)

            logger.info(f"找到{len(add_buttons)}个添加按钮")
            return add_buttons

        except Exception as e:
            logger.error(f"查找添加按钮失败: {e}")
            return []

    def _find_education_add_button(self, add_buttons):
        """从添加按钮中找到教育相关的按钮"""
        for button in add_buttons:
            try:
                # 检查按钮文本和上下文
                button_text = button.text.lower()
                parent_text = button.find_element(By.XPATH, '../..').text.lower()

                # 判断是否是教育相关的添加按钮
                education_keywords = ['教育', '学历', '学校', '专业', 'education', 'school']

                context_text = button_text + ' ' + parent_text
                if any(keyword in context_text for keyword in education_keywords):
                    logger.info(f"找到教育相关添加按钮: {button_text}")
                    return button

            except:
                continue

        # 如果没找到特定的教育添加按钮，返回第一个添加按钮
        if add_buttons:
            logger.info("未找到特定教育添加按钮，使用第一个添加按钮")
            return add_buttons[0]

        return None

    def _update_form_analysis_after_add(self):
        """添加新表单后更新分析结果"""
        try:
            logger.info("重新分析页面表单...")
            # 重新获取表单元素
            new_elements = self.web_automation.get_form_elements()

            # 找出新增的元素（简单比较数量）
            if len(new_elements) > len(self.form_elements):
                logger.info(f"检测到新增了{len(new_elements) - len(self.form_elements)}个表单元素")
                self.form_elements = new_elements

                # 重新构建分析结果
                self.form_analysis = self._build_form_analysis()

        except Exception as e:
            logger.error(f"更新表单分析失败: {e}")

    def _fill_additional_education(self, education_index):
        """填写额外的教育经历"""
        try:
            education_data = self.resume_data['education'][education_index]
            logger.info(f"填写第{education_index+1}段教育经历: {education_data.get('school', '未知学校')}")

            # 获取最新的教育相关字段（通常是页面底部的新增字段）
            education_fields = []

            # 查找最近添加的教育字段
            for field_data in self.form_analysis['text_fields']:
                field_id = field_data['identifier'].lower()
                if any(keyword in field_id for keyword in ['学校', '专业', '学历', 'school', 'major', 'degree']):
                    education_fields.append(field_data)

            # 对教育字段按页面位置排序（通过元素位置）
            education_fields.sort(key=lambda x: self._get_element_position(x['element_info']['element']))

            # 只填写最后几个教育字段（新增的）
            fields_per_education = 3  # 假设每段教育经历有3个字段：学校、专业、学历
            start_index = max(0, len(education_fields) - fields_per_education)

            for field_data in education_fields[start_index:]:
                field_id = field_data['identifier'].lower()

                if any(keyword in field_id for keyword in ['学校', 'school']):
                    value = education_data.get('school', '')
                elif any(keyword in field_id for keyword in ['专业', 'major']):
                    value = education_data.get('major', '')
                elif any(keyword in field_id for keyword in ['学历', 'degree']):
                    value = education_data.get('degree', '')
                else:
                    continue

                if value:
                    logger.info(f"填写教育字段 {field_data['identifier']}: {value}")
                    element = field_data['element_info']['element']
                    self.web_automation.fill_text_field(element, value)
                    time.sleep(0.5)

        except Exception as e:
            logger.error(f"填写额外教育经历失败: {e}")

    def _get_element_position(self, element):
        """获取元素在页面中的位置"""
        try:
            return element.location['y']
        except:
            return 0

    def _fill_single_text_field(self, field_data: Dict):
        """填写单个文本字段"""
        element_info = field_data['element_info']
        element = element_info['element']

        # 获取建议的值
        suggestion_info = self._get_suggested_value_for_text_field(field_data)

        if not suggestion_info or not suggestion_info.get('value'):
            logger.debug("未找到合适的值，跳过此字段")
            return

        # 保存原始建议
        field_identifier = field_data['identifier']
        self.original_suggestions[field_identifier] = suggestion_info

        suggested_value = suggestion_info['value'].strip()
        logger.info(f"填写文本值: {suggested_value} (置信度: {suggestion_info.get('confidence', 'unknown')})")

        # 根据输入类型填写
        input_type = field_data.get('input_type', 'text')
        if input_type in ['text', 'email', 'tel', 'number']:
            self.web_automation.fill_text_field(element, suggested_value)
        elif input_type == 'date':
            date_value = self._format_date(suggested_value)
            self.web_automation.handle_date_picker(element, date_value)
        else:
            self.web_automation.fill_text_field(element, suggested_value)

    def _fill_single_dropdown_field(self, field_data: Dict):
        """填写单个下拉菜单字段"""
        element_info = field_data['element_info']
        element = element_info['element']
        options = field_data.get('options', [])

        if not options:
            logger.warning(f"下拉菜单 '{field_data['identifier']}' 没有可选项，跳过")
            return

        # 使用AI从选项中选择
        selection_info = self._get_dropdown_selection(field_data, options)

        if selection_info:
            field_identifier = field_data['identifier']
            best_index = selection_info['index']
            selected_value = selection_info['value']

            # 验证索引有效性
            if 0 <= best_index < len(options):
                # 保存原始建议
                self.original_suggestions[field_identifier] = selection_info

                logger.info(f"选择下拉菜单第{best_index+1}个选项: {selected_value} (置信度: {selection_info['confidence']})")

                # 执行选择
                if element_info['type'] == 'select':
                    self.web_automation.select_dropdown(element, value=selected_value)
                else:
                    self.web_automation.select_dropdown(element, index=best_index)
            else:
                logger.warning(f"AI选择的索引{best_index}无效，跳过此字段")
        else:
            logger.warning(f"无法为下拉菜单 '{field_data['identifier']}' 获取选择建议")

    def _get_field_identifier(self, element_info: Dict) -> str:
        """获取字段标识符"""
        return (element_info.get('label', '') or
                element_info.get('name', '') or
                element_info.get('placeholder', '') or
                element_info.get('aria-label', '') or
                '未知字段')

    def _get_suggested_value_for_text_field(self, field_data: Dict) -> Dict[str, str]:
        """获取文本字段的建议值"""
        field_identifier = field_data['identifier']

        # 首先检查学习数据
        learned_suggestion = self.learning_module.get_learned_suggestion(field_data['element_info'])
        if learned_suggestion:
            logger.info(f"使用学习到的建议: {learned_suggestion}")
            return learned_suggestion

        # 尝试从简历中直接匹配
        direct_match = self._try_direct_match(field_data['element_info'])
        if direct_match:
            return {
                'value': direct_match,
                'confidence': 'high',
                'reasoning': '直接从简历匹配'
            }

        # 使用AI建议
        logger.info("使用AI生成文本字段建议值")
        resume_content = json.dumps(self.resume_data, ensure_ascii=False)
        field_info = {
            'label': field_data.get('label', ''),
            'name': field_data.get('name', ''),
            'placeholder': field_data.get('placeholder', ''),
            'type': field_data['type'],
            'input_type': field_data.get('input_type', '')
        }

        try:
            suggestion = self.vision_api.suggest_field_value(field_info, resume_content)
            # 对文本字段进行长度验证
            if suggestion.get('value'):
                value = suggestion['value'].strip()
                if len(value) > 100:  # 文本字段最多100字符
                    value = value[:100].strip()
                    logger.warning(f"AI建议值过长，截断至100字符: {value}")
                suggestion['value'] = value
            return suggestion
        except Exception as e:
            logger.error(f"AI建议失败: {e}")
            return {
                'value': '',
                'confidence': 'low',
                'reasoning': f'AI建议失败: {str(e)}'
            }

    def _get_dropdown_selection(self, field_data: Dict, options: List[str]) -> Dict[str, Any]:
        """获取下拉菜单的选择建议"""
        if not options:
            return None

        field_identifier = field_data['identifier']

        # 首先检查学习数据
        learned_suggestion = self.learning_module.get_learned_suggestion(field_data['element_info'])
        if learned_suggestion:
            # 对于下拉菜单，需要将学习到的值转换为选项索引
            learned_value = learned_suggestion['value']
            for i, option in enumerate(options):
                if option == learned_value:
                    logger.info(f"使用学习到的下拉菜单选择: {learned_value}")
                    return {
                        'index': i,
                        'value': learned_value,
                        'confidence': 'high',
                        'reasoning': '基于学习数据'
                    }

        # 验证选项列表
        valid_options = [opt for opt in options if opt and opt.strip()]
        if not valid_options:
            logger.warning(f"下拉菜单 '{field_identifier}' 没有有效选项")
            return None

        # 限制选项数量，避免传递过多选项给AI
        display_options = valid_options[:30] if len(valid_options) > 30 else valid_options
        if len(valid_options) > 30:
            logger.warning(f"下拉菜单选项过多({len(valid_options)}个)，只分析前30个")

        # 构建相关的简历信息
        relevant_resume_info = self._extract_relevant_resume_info(field_identifier)

        # 获取当前页面截图用于分析
        screenshot_path = None
        try:
            screenshot_path = self.web_automation.take_screenshot(f"dropdown_analysis_{field_identifier}")
            logger.info(f"为下拉菜单分析拍摄截图: {screenshot_path}")
        except Exception as e:
            logger.warning(f"拍摄下拉菜单分析截图失败: {e}")

        logger.info(f"正在AI分析下拉菜单选项:")
        logger.info(f"  字段: {field_identifier}")
        logger.info(f"  选项数量: {len(display_options)}")
        logger.info(f"  前5个选项: {display_options[:5]}")

        try:
            # 传递字段信息和截图给AI分析
            selection_info = self.vision_api.analyze_dropdown_options(
                options=display_options,
                resume_info=relevant_resume_info,
                field_info=field_data,
                screenshot_path=screenshot_path
            )

            # 验证AI返回的索引
            ai_index = selection_info.get('index', 0)
            if isinstance(ai_index, int) and 0 <= ai_index < len(display_options):
                actual_value = display_options[ai_index]

                # 双重验证：确保选择的值确实在原始选项列表中
                if actual_value in valid_options:
                    original_index = valid_options.index(actual_value)

                    logger.info(f"AI选择下拉菜单选项: [{ai_index}] {actual_value} (置信度: {selection_info.get('confidence', 'medium')})")

                    return {
                        'index': original_index,  # 使用在原始列表中的索引
                        'value': actual_value,
                        'confidence': selection_info.get('confidence', 'medium'),
                        'reasoning': selection_info.get('reasoning', 'AI自动选择')
                    }
                else:
                    logger.warning(f"AI选择的值 '{actual_value}' 不在原始选项列表中")
            else:
                logger.warning(f"AI返回的索引{ai_index}无效，使用第一个选项")

        except Exception as e:
            logger.error(f"AI分析下拉菜单选项失败: {e}")

        # 如果AI分析失败，尝试简单的文本匹配
        try:
            resume_text = relevant_resume_info.lower()
            field_name = field_data.get('identifier', '').lower()

            # 根据字段名称进行智能匹配
            best_match_index = self._smart_option_matching(field_name, resume_text, valid_options)
            if best_match_index is not None:
                return {
                    'index': best_match_index,
                    'value': valid_options[best_match_index],
                    'confidence': 'medium',
                    'reasoning': '基于字段名称和简历内容的智能匹配'
                }
        except Exception as e:
            logger.warning(f"智能匹配失败: {e}")

        # 默认选择第一个选项
        return {
            'index': 0,
            'value': valid_options[0],
            'confidence': 'low',
            'reasoning': '默认选择第一个选项'
        }

    def _extract_relevant_resume_info(self, field_identifier: str) -> str:
        """根据字段标识提取相关的简历信息"""
        field_lower = field_identifier.lower()
        relevant_info = {}

        # 基本信息相关
        if any(keyword in field_lower for keyword in ['姓名', 'name', '名字']):
            relevant_info['name'] = self.resume_data['basic_info'].get('name', '')
        elif any(keyword in field_lower for keyword in ['电话', 'phone', 'tel', '手机']):
            relevant_info['phone'] = self.resume_data['basic_info'].get('phone', '')
        elif any(keyword in field_lower for keyword in ['邮箱', 'email', 'mail']):
            relevant_info['email'] = self.resume_data['basic_info'].get('email', '')

        # 教育相关
        elif any(keyword in field_lower for keyword in ['学校', 'school', '院校', '大学', '毕业']):
            if self.resume_data['education']:
                relevant_info['education'] = {
                    'school': self.resume_data['education'][0].get('school', ''),
                    'major': self.resume_data['education'][0].get('major', ''),
                    'degree': self.resume_data['education'][0].get('degree', '')
                }
        elif any(keyword in field_lower for keyword in ['专业', 'major', '学科']):
            if self.resume_data['education']:
                relevant_info['major'] = self.resume_data['education'][0].get('major', '')
        elif any(keyword in field_lower for keyword in ['学历', 'degree', '学位']):
            if self.resume_data['education']:
                relevant_info['degree'] = self.resume_data['education'][0].get('degree', '')

        # 工作经验相关
        elif any(keyword in field_lower for keyword in ['工作', 'work', '公司', 'company', '职位', 'position']):
            if self.resume_data.get('experience', {}).get('internship'):
                relevant_info['experience'] = self.resume_data['experience']['internship'][0]

        # 技能相关
        elif any(keyword in field_lower for keyword in ['技能', 'skill', '能力', '语言']):
            relevant_info['skills'] = self.resume_data.get('skills', {})

        # 如果没有找到特定相关信息，返回基本信息
        if not relevant_info:
            relevant_info = self.resume_data['basic_info']

        return json.dumps(relevant_info, ensure_ascii=False)

    def _smart_option_matching(self, field_name: str, resume_text: str, options: List[str]) -> Optional[int]:
        """智能匹配下拉菜单选项"""
        field_name = field_name.lower()
        resume_text = resume_text.lower()

        # 定义匹配规则
        matching_rules = {
            # 学历匹配
            'degree': {
                'keywords': ['学历', '学位', 'degree', '教育程度'],
                'mappings': {
                    '博士': ['博士', 'phd', 'doctor', '博士研究生'],
                    '硕士': ['硕士', 'master', '研究生', '硕士研究生'],
                    '本科': ['本科', 'bachelor', '学士', '大学本科'],
                    '专科': ['专科', 'college', '大专', '高职'],
                    '高中': ['高中', 'high school', '中学']
                }
            },
            # 性别匹配
            'gender': {
                'keywords': ['性别', 'gender', '男女'],
                'mappings': {
                    '男': ['男', 'male', '先生'],
                    '女': ['女', 'female', '女士']
                }
            },
            # 政治面貌匹配
            'political': {
                'keywords': ['政治', '党员', '面貌', 'political'],
                'mappings': {
                    '中共党员': ['党员', '中共党员', '共产党员'],
                    '共青团员': ['团员', '共青团员', '共青团'],
                    '群众': ['群众', '无党派', '普通群众']
                }
            },
            # 工作年限匹配
            'experience': {
                'keywords': ['工作', '经验', '年限', 'experience', '工龄'],
                'mappings': {
                    '应届生': ['应届', '无经验', '0年', '在校'],
                    '1年以下': ['1年以下', '半年', '6个月'],
                    '1-3年': ['1年', '2年', '3年'],
                    '3-5年': ['3年', '4年', '5年'],
                    '5年以上': ['5年以上', '6年', '7年', '8年', '9年', '10年']
                }
            }
        }

        # 检查字段类型
        for rule_type, rule_data in matching_rules.items():
            if any(keyword in field_name for keyword in rule_data['keywords']):
                # 在选项中查找匹配
                for option_idx, option in enumerate(options):
                    option_lower = option.lower()

                    # 检查每个映射
                    for target_value, keywords in rule_data['mappings'].items():
                        if any(keyword in option_lower for keyword in keywords):
                            # 检查简历中是否包含相关信息
                            if any(keyword in resume_text for keyword in keywords):
                                logger.info(f"智能匹配成功: {field_name} -> {option} (基于{rule_type}规则)")
                                return option_idx

                # 如果没有找到精确匹配，尝试模糊匹配
                for option_idx, option in enumerate(options):
                    option_lower = option.lower()
                    # 计算相似度
                    similarity_score = self._calculate_text_similarity(resume_text, option_lower)
                    if similarity_score > 0.3:  # 相似度阈值
                        logger.info(f"模糊匹配成功: {field_name} -> {option} (相似度: {similarity_score:.2f})")
                        return option_idx

        return None

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        # 简单的基于共同词汇的相似度计算
        words1 = set(text1.split())
        words2 = set(text2.split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def _try_direct_match(self, element_info: Dict) -> str:
        """尝试直接匹配简历内容"""
        field_identifiers = [
            element_info.get('label', ''),
            element_info.get('name', ''),
            element_info.get('placeholder', ''),
            element_info.get('aria-label', '')
        ]

        # 连接所有标识符用于更好的匹配
        combined_identifier = ' '.join(filter(None, field_identifiers)).lower()
        logger.info(f"尝试直接匹配字段: {combined_identifier}")

        # 基本信息匹配 - 更精确的关键词匹配
        if any(keyword in combined_identifier for keyword in ['姓名', '名字', 'name', '真实姓名', '申请人姓名']):
            result = self.resume_data['basic_info'].get('name', '')
            logger.info(f"匹配到姓名字段，返回: {result}")
            return result

        elif any(keyword in combined_identifier for keyword in ['电话', '手机', 'phone', 'tel', '联系电话', '手机号码', '联系方式']):
            result = self.resume_data['basic_info'].get('phone', '')
            logger.info(f"匹配到电话字段，返回: {result}")
            return result

        elif any(keyword in combined_identifier for keyword in ['邮箱', 'email', 'mail', '电子邮件', '邮件地址']):
            result = self.resume_data['basic_info'].get('email', '')
            logger.info(f"匹配到邮箱字段，返回: {result}")
            return result

        # 教育信息匹配
        elif any(keyword in combined_identifier for keyword in ['学校', '院校', 'school', '大学', '毕业院校', '毕业学校', '学校名称']):
            if self.resume_data['education']:
                result = self.resume_data['education'][0].get('school', '')
                logger.info(f"匹配到学校字段，返回: {result}")
                return result

        elif any(keyword in combined_identifier for keyword in ['专业', 'major', '所学专业', '专业名称', '学科']):
            if self.resume_data['education']:
                result = self.resume_data['education'][0].get('major', '')
                logger.info(f"匹配到专业字段，返回: {result}")
                return result

        elif any(keyword in combined_identifier for keyword in ['学历', 'degree', '最高学历', '学位', '学历层次']):
            if self.resume_data['education']:
                result = self.resume_data['education'][0].get('degree', '')
                logger.info(f"匹配到学历字段，返回: {result}")
                return result

        elif 'gpa' in combined_identifier.lower() or '绩点' in combined_identifier:
            if self.resume_data['education']:
                result = self.resume_data['education'][0].get('gpa', '')
                logger.info(f"匹配到GPA字段，返回: {result}")
                return result

        # 工作经验匹配
        elif any(keyword in combined_identifier for keyword in ['公司', '单位', 'company', '工作单位', '实习公司']):
            internships = self.resume_data.get('experience', {}).get('internship', [])
            if internships:
                result = internships[0].get('company', '')
                logger.info(f"匹配到公司字段，返回: {result}")
                return result

        elif any(keyword in combined_identifier for keyword in ['职位', '岗位', 'position', 'title', '职务', '实习职位']):
            internships = self.resume_data.get('experience', {}).get('internship', [])
            if internships:
                result = internships[0].get('position', '')
                logger.info(f"匹配到职位字段，返回: {result}")
                return result

        # 对于无法直接匹配的字段，返回空字符串而不是姓名
        logger.info(f"字段 '{combined_identifier}' 无法直接匹配，返回空字符串")
        return ''

    def _format_date(self, date_str: str) -> str:
        """格式化日期"""
        # 尝试解析常见的日期格式
        common_formats = [
            "%Y年%m月%d日",
            "%Y年%m月",
            "%Y-%m-%d",
            "%Y/%m/%d"
        ]

        for fmt in common_formats:
            try:
                date_obj = datetime.strptime(date_str, fmt)
                # 返回标准格式
                return date_obj.strftime("%Y-%m-%d")
            except:
                continue

        # 如果无法解析，返回原始字符串
        return date_str

    def wait_for_user_corrections(self):
        """等待用户修正并学习"""
        print("\n" + "="*60)
        print("✅ 自动填写已完成！")
        print("="*60)
        print("📝 请检查并修改填写结果：")
        print("1. 检查所有字段是否填写正确")
        print("2. 根据需要修改不准确的内容")
        print("3. 修改完成后按回车键，系统将学习您的修改")
        print("="*60)

        input("按回车键开始学习您的修改...")

        # 捕获用户修改后的表单状态
        logger.info("开始学习用户修改...")
        final_form_state = self.learning_module.capture_current_form_state(self.web_automation)

        # 学习用户的修正
        self.learning_module.learn_from_user_corrections(
            self.original_suggestions,
            final_form_state
        )

        print("\n🎓 学习完成！系统已记录您的填写偏好。")
        print("下次填写相似表单时，系统会优先使用您偏好的值。")

        # 显示学习统计
        stats = self.learning_module.get_learning_stats()
        print(f"📊 当前学习统计: 已学习{stats['learned_fields']}个字段类型")

        print("\n按回车键关闭浏览器...")
        input()