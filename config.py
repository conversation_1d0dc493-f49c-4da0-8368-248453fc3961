"""
配置文件 - API密钥和系统设置
"""

# API 配置
API_CONFIG = {
    'openai': {
        'api_key': 'your-openai-api-key',
        'base_url': 'https://api.openai.com/v1',
        'vision_model': 'gpt-4-vision-preview',
        'text_model': 'gpt-4-turbo-preview'
    },
    'qwen': {
        'api_key': 'sk-1781098f862a4e028e050d9f1adeeaff', 
        'base_url': 'https://dashscope.aliyuncs.com/compatible-mode/v1',
        'vision_model': 'qwen-vl-max',
        'text_model': 'qwen-max'
    },
    'google': {
        'api_key': 'AIzaSyB_P0Xp5mdDzx1XCLTNGTvJeZqjBtcl_YQ',
        'vision_model': 'gemini-2.0-flash'
    }
}

# 使用的API提供商
ACTIVE_API = 'qwen'  # 可选: 'openai', 'qwen', 'google'

# 系统设置
SYSTEM_CONFIG = {
    'browser': 'chrome',  # 可选: 'chrome', 'edge', 'firefox'
    'wait_time': 2,  # 元素等待时间(秒)
    'screenshot_dir': 'screenshots',  # 截图保存目录
    'log_level': 'INFO',  # 日志级别
    'headless': False,  # 是否无头模式
}

# 简历文件路径
RESUME_PATH = 'resume/markdown.md'

# Prompt模板
PROMPTS = {
    'field_detection': """
    分析这个网页截图，识别出所有需要填写的表单字段。
    对于每个字段，请提供：
    1. 字段名称/标签
    2. 字段类型（文本框、下拉菜单、单选框、复选框、日期选择器等）
    3. 是否必填
    4. 字段的大概位置描述
    
    请以JSON格式返回结果。
    """,
    
    'value_suggestion': """
    基于以下简历内容和表单字段信息，为字段建议一个简洁的填写值。

    简历内容：
    {resume_content}

    字段信息：
    {field_info}

    请返回一个JSON格式的结果，包含：
    {{
        "suggested_value": "建议的简洁填写值",
        "confidence": "置信度（high/medium/low）",
        "reasoning": "选择这个值的原因"
    }}

    重要要求：
    - suggested_value必须非常简洁，最多20个字符，适合直接填入表单
    - 对于姓名字段，只返回姓名本身，如"张三"
    - 对于电话字段，只返回电话号码，如"13812345678"
    - 对于邮箱字段，只返回邮箱地址，如"<EMAIL>"
    - 对于学校字段，只返回学校名称，如"清华大学"
    - 对于专业字段，只返回专业名称，如"计算机科学"
    - 如果无法从简历中找到对应信息，suggested_value必须返回空字符串""
    - 绝对不要返回解释性文字或描述
    """,
    
    'dropdown_analysis': """
    基于简历信息选择最合适的下拉菜单选项。

    下拉菜单选项：
    {options}

    简历信息：
    {resume_info}

    请返回JSON格式：
    {{
        "selected_index": 选择的选项索引号（数字），
        "selected_value": "选择的选项文本",
        "confidence": "置信度（high/medium/low）",
        "reasoning": "选择理由"
    }}

    如果没有合适的选项，返回index为0。
    """
} 