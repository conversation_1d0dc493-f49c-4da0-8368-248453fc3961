/* 初始化ArkAI的样式 */
:host {
  --page-bg: #0e0e10;
  --primary-bg: #13141f;
  --secondary-bg: #1a1b27;
  --primary-color: #5e61c7;
  --primary-light: #8c7bf9;
  --text-primary: #e1e1e6;
  --text-secondary: #8e8e96;
  --border-color: #27272a;
  
  /* 添加状态颜色 */
  --success-color: #10B981;  /* Message 中的绿色 */
  --warning-color: #F59E0B;  /* Message 中的黄色 */
  --error-color: #EF4444;    /* Message 中的红色 */

}

/* 在Shadow DOM中，不需要#ark-ai前缀，直接写选择器即可 */

/* 设置盒模型 */
*,
*::before,
*::after {
  box-sizing: content-box;  /* 不需要!important了，因为Shadow DOM有样式隔离 */
}

/* 输入框基础样式 */
input {
  height: 16px;
  line-height: 16px;
  font-size: 14px;
}

button {
  padding: 0;
}

/* 移除输入框focus时的外边框高亮 */
input:focus,
textarea:focus,
select:focus {
  outline: none;
}

/* 字体设置 */
*:not(i) {
  font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, Tahoma, PingFang SC, Microsoft Yahei, Arial, Hiragino Sans GB, sans-serif;
}

/* Chrome自动填充样式覆盖 - 这个还是需要-webkit前缀和!important */
input:-webkit-autofill {
  background-color: transparent !important;
  color: var(--text-primary) !important;
  transition: background-color 0s 50000s, color 0s 50000s !important;
  -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
}



#logo-button {
  position: fixed;
  bottom: 40px;
  right: 40px;
  z-index: 100000000;
  background-color: #111;
  /* color: white; */
  border: 1px solid #222;
  border-radius: 20px;
  cursor: pointer !important;
  width: 50px;
  height: 50px;
  padding: 0;
  /* line-height: 50px;
  text-align: center;
  font-size: 22px;
  font-weight: bold; */
  overflow: hidden;
  transition: display 0.2s ease;
  /* display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 50%;
  background: white;
  box-shadow: 0 2px 12px rgba(0,0,0,0.15);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease; */
  animation: logo-button-breathe 3s ease-in-out infinite;
}

#logo-button:hover {
  cursor: pointer !important;
  box-shadow: 0 4px 20px rgba(0,0,0,0.2);
}

#logo-button img {
  width: 100%;
  height: 100%;
  vertical-align: initial;
  /* border-radius: 50%; */
}

@keyframes logo-button-breathe {
  0% {
    /* opacity: 1; */
    transform: scale(1);
  }
  10% {
    /* opacity: 0.97; */
    transform: scale(1.02);
  }
  15% {
    /* opacity: 0.9; */
    transform: scale(1.1);
  }
  20% {
    /* opacity: 0.92; */
    transform: scale(1.05);
  }
  25% {
    /* opacity: 0.9; */
    transform: scale(1.1);
  }
  30% {
    /* opacity: 0.97; */
    transform: scale(1.02);
  }
  40% {
    /* opacity: 1; */
    transform: scale(1);
  }
  100% {
    /* opacity: 1; */
    transform: scale(1);
  }
}
/* @keyframes logo-button-breathe {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  10% {
    opacity: 0.97;
    transform: scale(1.02);
  }
  20% {
    opacity: 0.9;
    transform: scale(1.1);
  }
  30% {
    opacity: 0.97;
    transform: scale(1.02);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
} */



#resume-window {
  position: fixed;
  bottom: 40px;
  right: 40px;

  /* 这里的调整 要联动js中 adjustWindowHeight的调整 */
  max-height: calc(100vh - 80px);  /* 窗口最大高度为视口高度减去上下边距 */
  min-height: 600px;  /* 设置最小高度，避免太小 */
  height: 700px;  /* 默认高度 */

  width: 340px;
  border-radius: 20px;
  border: 1px solid #333;
  z-index: 100000001;
  background: rgba(0, 0, 0, 0.8);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), 0 0 40px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  flex-direction: column;
  display: none;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  transition: opacity 0.2s ease, height 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, Tahoma, PingFang SC, Microsoft Yahei, Arial, Hiragino Sans GB, sans-serif;
  -webkit-font-smoothing: antialiased; /* 文字抗锯齿 深色背景文字更好看 颜色会变淡 */
  line-height: normal;
  text-align: left;
}
#resume-window.show {
  display: flex;
}
#resume-window.min {
  min-height: 50px;  /* 设置最小高度，避免太小 */
  height: 50px;
}
#resume-window h4{
  margin: 0;
}

.resume-header {
  background-color: #111;
  color: #ddd;
  display: flex;
  justify-content: center; /* 居中对齐 */
  align-items: center;
  height: 26px;
  font-size: 16px;
  font-weight: 600;
  padding: 4px 12px;
  position: relative; /* 允许绝对定位的按钮 */
  /* gap: 8px; */
}
.resume-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #ddd;
}
.resume-header img {
  width: 28px;
  height: 28px;
}
.resume-header:hover {
  cursor: pointer !important;
}
.resume-header button {
  position: absolute;
  right: 12px; /* 将按钮放在右侧 */
  background: transparent;
  color: #888;
  border: none;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  transition: color 0.2s ease, transform 0.2s ease;
}
.resume-header button:hover {
  color: #ddd;
}
.resume-header button i {
  transition: transform 0.2s ease;
}
/* 
#resume-window.min .resume-header button i {
  transform: rotate(180deg);
} */

.resume-display {
  display: flex;
  flex-direction: column;
  padding: 8px 16px;
}

.job-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  gap: 10px; /* 增加这一行，设置输入框之间的间距 */
}

.job-info input {
  width: calc(50% - 5px) !important; /* 修改这一行，调整宽度以适应间距 */
  padding: 8px !important;
  color: #ddd !important;
  border: 1px solid #222 !important;
  border-radius: 4px;
}
.job-info .normal-bg {
  background-color: rgba(0, 0, 0, 0.4) !important;
}

@keyframes info-input-breathe {
  0% {
    background-color: rgba(0, 0, 0, 0.4);
  }
  50% {
    /* background-color: #361954; */
    /* background-color: #3f1a66; */
    background-color: var(--primary-color);
  }
  100% {
    background-color: rgba(0, 0, 0, 0.4);
  }
}
.job-info input.breathing-bg {
  animation: info-input-breathe 1s infinite;
}

.resume-content {
  display: flex;
  flex-direction: column;
}

.resume-field {
  display: flex;
  align-items: center;
  margin: 0;
  margin-bottom: 8px;
  justify-content: space-between; /* 添加这行 */
}

.resume-field label {
  font-size: 16px;
  margin: 0;
  margin-right: 8px;
  color: #ddd;
}

/* 添加以下新样式 */
.beautify-resume {
  display: flex;
  align-items: center;
}

.beautify-resume input[type="checkbox"] {
  display: none;
}

.beautify-resume label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #888; /* 浅灰色 */
  transition: color 0.3s ease;
  position: relative;
  padding-left: 18px; /* 减小这个值，从25px改为22px */
  margin: 0;
}

.beautify-resume label i.fa-square {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  color: #555;
  font-size: 16px; /* 稍微减小图标大小，从18px改为16px */
}

.beautify-resume label i.fa-check {
  display: none;
  color: #888;
  position: absolute;
  left: 2px; /* 稍微调整这个值，从3px改为2px */
  top: 50%;
  transform: translateY(-50%);
  font-size: 11px; /* 稍微减小勾选图标大小，从12px改为11px */
}

.beautify-resume label i.fa-magic {
  margin-right: 4px; /* 减小这个值，从5px改为4px */
  font-size: 14px; /* 稍微减小魔法棒图标大小，从16px改为14px */
}

.beautify-resume input[type="checkbox"]:checked + label i.fa-square {
  /* color: #7f38ba; */
  color: var(--primary-color);
}

.beautify-resume input[type="checkbox"]:checked + label i.fa-check {
  color: #999; /* 选中时的颜色 */
  display: block; /* 改为 block */
}

.beautify-resume input[type="checkbox"]:checked + label {
  /* color: #a95ee6; */
  color: var(--primary-light);
}

.beautify-resume input[type="checkbox"]:checked + label i.fa-magic {
  /* color: #a95ee6; */
  color: var(--primary-light);
}

#resume-version {
  display: flex;
  align-items: center;
  padding: 6px;
  background-color: rgba(0, 0, 0, 0.4) !important;
  color: #ddd !important;
  border: 1px solid #222 !important;
  border-radius: 4px;
  margin-bottom: 1px;
  font-size: 14px;
}

#resume-editor {
  flex-grow: 1;
  min-height: 400px;
  border: 1px solid #333;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
}


/* 快速链接按钮样式 */
.quick-links {
  display: flex;
  justify-content: space-between;
  margin: 8px 0;
  gap: 8px; /* 稍微减小间距，适应三个按钮 */
}

.quick-link-btn {
  flex: 1;
  background-color: #222;
  color: #ddd;
  border: 1px solid #333;
  border-radius: 4px;
  padding: 8px 0;
  cursor: pointer;
  font-size: 13px; /* 稍微减小字体大小，适应三个按钮 */
  transition: all 0.2s ease;
}

.quick-link-btn:hover {
  background-color: var(--primary-color);
  color: #fff;
}

/* 新版本通知样式 */
.version-notification {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  justify-content: center; /* 水平居中对齐 */
  background-color: var(--primary-color);
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  margin: 8px 0;
  font-size: 13px;
  animation: pulse 2s infinite;
  height: 24px;
}

.version-notification i {
  position: relative;
  top: 2px;
  font-size: 16px;
  line-height: 24px;
}

.version-notification span {
  line-height: 24px;
}

#update-now-btn {
  background-color: #fff;
  color: var(--primary-color);
  border: none;
  border-radius: 3px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  margin-left: 8px;
  transition: all 0.2s ease;
  float: right;
}

#update-now-btn:hover {
  background-color: #e2e8f0;
}

@keyframes pulse {
  0% {
    opacity: 0.9;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0.9;
  }
}


/* 底部功能按钮样式 */

.bottom-function {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 50px;
  padding: 0;
  border-radius: 20px;
  background: #222;
}

.bottom-function button {
  border: none;
  cursor: pointer;
}

#start-button {
  color: #eee;
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  border: none;
  cursor: pointer;
  font-size: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 19px; /* 小1像素 否则圆角边界会有点怪 */
  /* background: #090909; */
  /* background-color: #7600d6; */
  background-color: var(--primary-color);
  transition: background-color 0.2s ease, box-shadow 0.2s ease, color 0.2s ease, font-size 0.1s ease; /* 缩短字体大小变大的时间 */
  position: relative;
}

#start-button:hover {
  color: #fff;
  /* background: #8102e8; */
  background-color: var(--primary-light);
  box-shadow: 0 0 2px #111;
  transition: background-color 0.2s ease, box-shadow 0.2s ease, color 0.2s ease, transform 0.2s ease;
}

#start-button i {
  display: block;
  position: absolute;
  left: 54%;
  transform: translateX(-50%);
  transition: text-shadow 0.2s ease, font-size 0.1s ease; /* 缩短图标大小变大的时间 */
}

#start-button:hover i {
  text-shadow: 0 0 2px #eee;
  font-size: 22px; /* 确保图标也随按钮一起变大 */
  transition: text-shadow 0.2s ease, font-size 0.3s ease; /* 延长图标大小变小的时间 */
}

@keyframes start-button-breathe {
  0% {
    /* background-color: #8102e8; */
    background-color: var(--primary-color);
  }
  50% {
    /* background-color: #9c23ff; */
    background-color: var(--primary-light);
  }
  100% {
    /* background-color: #8102e8; */
    background-color: var(--primary-color);
  }
}
#start-button.paused {
  /* background-color: #8102e8; */
  background-color: var(--primary-color);
  animation: start-button-breathe 1s ease-in-out infinite;
}
#start-button.paused:hover {
  /* background-color: #7600d6; */
  background-color: var(--primary-color);
  animation: none; /* 悬停时停止动画 */
}

/* 禁用状态效果 */
#start-button.ban {
  /* background-color: #8102e8; */
  background-color: var(--primary-color);
  cursor: default;
}
#start-button.ban:hover {
  /* background-color: #8102e8; */
  background-color: var(--primary-color);
  box-shadow: none;
}
#start-button.ban i {
  font-size: 20px;
  left: 50%;
}
#start-button.ban:hover i {
  text-shadow: none;
  font-size: 20px;
}



#state-text {
  flex-grow: 1;
  height: 40px;
  padding: 0 10px;
  background: #222;
  color: #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-radius: 4px;
  font-size: 14px;
  overflow-wrap: break-word; /* 允许长单词换行 */
  word-wrap: break-word; /* 兼容性考虑 */
  word-break: break-word; /* 在适当的位置换行 */
  white-space: normal; /* 允许正常换行 */
}

#close-window {
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  background: #222;
  color: #888;
  border: none;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  transition: color 0.2s ease, transform 0.2s ease;
}

#close-window:hover {
  color: #ddd;
}

#close-window i {
  transition: transform 0.2s ease;
}

/* #resume-window.min #close-window i {
  transform: rotate(180deg);
} */



/* SimpleMDE编辑器样式覆盖 */
.CodeMirror {
  /* 这里只是为了初始化高度 在js的adjustWindowHeight中会动态调整 */
  height: 390px !important;

  border: 1px solid #222 !important;
  border-radius: 4px !important;
  color: #ddd !important;
  font-size: 14px !important;
}
.CodeMirror.showNewVersion {
  /* 这里只是为了初始化高度 在js的adjustWindowHeight中会动态调整 */
  height: 350px !important;
}
.CodeMirror.normal-bg {
  background-color: rgba(0, 0, 0, 0.4) !important;
}
@keyframes code-mirror-breathe {
  0% {
    background-color: rgba(0, 0, 0, 0.4);
  }
  50% {
    /* background-color: #361954; */
    /* background-color: #3f1a66; */
    background-color: var(--primary-color);
  }
  100% {
    background-color: rgba(0, 0, 0, 0.4);
  }
}
.CodeMirror.breathing-bg {
  animation: code-mirror-breathe 1s infinite;
}
.CodeMirror .CodeMirror-selectedtext {
  background: #555 !important; /* 浅灰色 */
  color: #eee !important; /* 白色文字 */
}
.CodeMirror span {
  color: #eee !important; /* 白色文字 */
}
.CodeMirror .CodeMirror-code {
  font-size: 14px;
}
.CodeMirror .CodeMirror-code .cm-formatting-list {
  color: var(--primary-light) !important;
}
.CodeMirror .CodeMirror-code .cm-header {
  color: var(--primary-light) !important;
}
.CodeMirror .CodeMirror-code .cm-header-1 {
  font-size: 130% !important;
  line-height: 130% !important;
}
.CodeMirror .CodeMirror-code .cm-header-2 {
  font-size: 120% !important;
  line-height: 124% !important;
}
.CodeMirror .CodeMirror-code .cm-header-3 {
  font-size: 110% !important;
  line-height: 112% !important;
}
.CodeMirror .CodeMirror-code .cm-header-4 {
  font-size: 105% !important;
  line-height: 105% !important;
}
.CodeMirror .CodeMirror-cursor {
  border-left: 2px solid #aaa !important;
}

/* 直接针对 CodeMirror 滚动条的自定义样式 */
.CodeMirror-vscrollbar::-webkit-scrollbar,
.CodeMirror-hscrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: transparent;
}

.CodeMirror-vscrollbar::-webkit-scrollbar-track,
.CodeMirror-hscrollbar::-webkit-scrollbar-track {
  background: var(--secondary-bg);
  /* background: var(--primary-bg); */
  border-radius: 4px;
}

.CodeMirror-vscrollbar::-webkit-scrollbar-thumb,
.CodeMirror-hscrollbar::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.CodeMirror-vscrollbar::-webkit-scrollbar-thumb:hover,
.CodeMirror-hscrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--primary-light);
}

/* 覆盖 CodeMirror 原生滚动条样式 */
.CodeMirror-vscrollbar,
.CodeMirror-hscrollbar {
  position: absolute;
  z-index: 6;
}

.CodeMirror-vscrollbar {
  right: 0;
  top: 0;
  overflow-x: hidden;
  overflow-y: auto;
}

.CodeMirror-hscrollbar {
  bottom: 0;
  left: 0;
  overflow-y: hidden;
  overflow-x: auto;
}

.CodeMirror-scroll {
  overflow: auto !important;
  margin-bottom: -30px;
  margin-right: -30px;
  padding-bottom: 30px;
  height: 100%;
  outline: none;
  position: relative;
}

/* 优化 CodeMirror 内文本选择样式 */
.CodeMirror-selected, 
.CodeMirror-focused .CodeMirror-selected,
.CodeMirror-line::selection, 
.CodeMirror-line > span::selection, 
.CodeMirror-line > span > span::selection {
  background: rgba(94, 97, 199, 0.3) !important; /* 使用主色调的半透明版本 */
}

/* 代码镜像选中文本 */
.CodeMirror .CodeMirror-selectedtext {
  background: transparent !important; /* 避免双重背景 */
  color: #fff !important; /* 白色文字增强可读性 */
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.3); /* 轻微文字阴影增强可见度 */
}

.editor-statusbar {
  position: relative;
  top: -20px;
  height: 0;
  padding: 0 10px;
}






