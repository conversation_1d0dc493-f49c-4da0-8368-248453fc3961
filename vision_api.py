"""
视觉API接口 - 支持OpenAI、千问、Google等视觉模型
"""

import base64
import json
import logging
import os
from typing import Dict, Any, Optional
import requests
from config import API_CONFIG, ACTIVE_API, PROMPTS

logger = logging.getLogger(__name__)


class VisionAPI:
    """视觉API统一接口"""

    def __init__(self, provider: str = None):
        self.provider = provider or ACTIVE_API
        self.config = API_CONFIG.get(self.provider, {})

        if not self.config:
            raise ValueError(f"不支持的API提供商: {self.provider}")

    def encode_image(self, image_path: str) -> str:
        """将图片编码为base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"图片编码失败: {e}")
            raise

    def analyze_screenshot(self, image_path: str, prompt: str) -> Dict[str, Any]:
        """分析截图"""
        if self.provider == 'openai':
            return self._openai_analyze(image_path, prompt)
        elif self.provider == 'qwen':
            return self._qwen_analyze(image_path, prompt)
        elif self.provider == 'google':
            return self._google_analyze(image_path, prompt)
        else:
            raise ValueError(f"不支持的提供商: {self.provider}")

    def _openai_analyze(self, image_path: str, prompt: str) -> Dict[str, Any]:
        """使用OpenAI API分析图片"""
        base64_image = self.encode_image(image_path)

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.config['api_key']}"
        }

        payload = {
            "model": self.config['vision_model'],
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 1000
        }

        try:
            response = requests.post(
                f"{self.config['base_url']}/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            result = response.json()
            content = result['choices'][0]['message']['content']

            # 尝试解析JSON响应
            try:
                return json.loads(content)
            except:
                return {"raw_response": content}

        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            raise

    def _qwen_analyze(self, image_path: str, prompt: str) -> Dict[str, Any]:
        """使用千问API分析图片"""
        base64_image = self.encode_image(image_path)

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.config['api_key']}"
        }

        payload = {
            "model": self.config['vision_model'],
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 1000
        }

        try:
            response = requests.post(
                f"{self.config['base_url']}/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            result = response.json()
            content = result['choices'][0]['message']['content']

            # 尝试解析JSON响应
            try:
                return json.loads(content)
            except:
                return {"raw_response": content}

        except Exception as e:
            logger.error(f"千问API调用失败: {e}")
            raise

    def _google_analyze(self, image_path: str, prompt: str) -> Dict[str, Any]:
        """使用Google Gemini API分析图片"""
        # Google Gemini API实现
        # 这里需要根据实际的Gemini API文档实现
        raise NotImplementedError("Google Gemini API尚未实现")

    def detect_form_fields(self, screenshot_path: str) -> Dict[str, Any]:
        """检测表单字段"""
        prompt = PROMPTS['field_detection']
        return self.analyze_screenshot(screenshot_path, prompt)

    def suggest_field_value(self, field_info: Dict, resume_content: str) -> Dict[str, str]:
        """建议字段值"""
        prompt = PROMPTS['value_suggestion'].format(
            resume_content=resume_content,
            field_info=json.dumps(field_info, ensure_ascii=False)
        )

        # 使用文本模型而不是视觉模型
        response = self._text_completion(prompt)

        # 尝试解析JSON响应
        try:
            result = json.loads(response)
            suggested_value = result.get('suggested_value', '').strip()

            # 强制限制值的长度，确保适合表单填写
            if len(suggested_value) > 50:
                suggested_value = suggested_value[:50].strip()
                logger.warning(f"AI建议值过长，已截断至50字符: {suggested_value}")

            return {
                'value': suggested_value,
                'confidence': result.get('confidence', 'low'),
                'reasoning': result.get('reasoning', '')
            }
        except json.JSONDecodeError:
            logger.warning(f"AI响应无法解析为JSON，原始响应: {response}")

            # 改进的fallback逻辑 - 提取简短的关键信息
            cleaned_response = response.strip()

            # 移除常见的AI回复前缀
            prefixes_to_remove = ['根据', '基于', '建议', '推荐', '应该', '可以', '我认为', '我建议']
            for prefix in prefixes_to_remove:
                if cleaned_response.startswith(prefix):
                    cleaned_response = cleaned_response[len(prefix):].strip()

            # 取第一行或第一句，并限制长度
            lines = cleaned_response.split('\n')
            first_line = lines[0].strip() if lines else ''

            # 进一步提取关键词（如果是描述性文本）
            if len(first_line) > 20:
                # 尝试提取引号内的内容
                import re
                quoted_matches = re.findall(r'[""""]([^""""]++)[""""]', first_line)
                if quoted_matches:
                    first_line = quoted_matches[0]
                else:
                    # 如果没有引号，取前20个字符
                    first_line = first_line[:20].strip()

            # 最终长度检查
            final_value = first_line[:30].strip() if first_line else ''

            return {
                'value': final_value,
                'confidence': 'low',
                'reasoning': '从非结构化响应中提取关键信息'
            }

    def _text_completion(self, prompt: str) -> str:
        """文本补全API调用"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.config['api_key']}"
        }

        payload = {
            "model": self.config.get('text_model', self.config['vision_model']),
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 500
        }

        try:
            response = requests.post(
                f"{self.config['base_url']}/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']

        except Exception as e:
            logger.error(f"文本API调用失败: {e}")
            raise

    def analyze_dropdown_options(self, options: list, resume_info: str, field_info: Dict = None, screenshot_path: str = None) -> Dict[str, Any]:
        """分析下拉菜单选项"""
        if not options:
            return {
                'index': 0,
                'value': '',
                'confidence': 'low',
                'reasoning': '没有可选项'
            }

        # 验证选项列表
        valid_options = [opt for opt in options if opt and opt.strip()]
        if not valid_options:
            return {
                'index': 0,
                'value': options[0] if options else '',
                'confidence': 'low',
                'reasoning': '没有有效选项'
            }

        max_index = len(valid_options) - 1
        field_info_str = json.dumps(field_info or {}, ensure_ascii=False)

        # 如果有截图，使用带截图的分析
        if screenshot_path and os.path.exists(screenshot_path):
            prompt = PROMPTS['dropdown_analysis_with_screenshot'].format(
                options=json.dumps(valid_options, ensure_ascii=False),
                resume_info=resume_info,
                field_info=field_info_str,
                max_index=max_index
            )

            try:
                result = self.analyze_screenshot(screenshot_path, prompt)
                if isinstance(result, dict) and 'selected_index' in result:
                    index = result.get('selected_index', 0)
                    if 0 <= index < len(valid_options):
                        return {
                            'index': index,
                            'value': result.get('selected_value', valid_options[index]),
                            'confidence': result.get('confidence', 'medium'),
                            'reasoning': result.get('reasoning', '基于截图和选项分析')
                        }
            except Exception as e:
                logger.warning(f"使用截图分析下拉菜单失败，回退到文本分析: {e}")

        # 使用纯文本分析
        prompt = PROMPTS['dropdown_analysis'].format(
            options=json.dumps(valid_options, ensure_ascii=False),
            resume_info=resume_info,
            field_info=field_info_str,
            max_index=max_index
        )

        response = self._text_completion(prompt)

        # 尝试解析JSON响应
        try:
            result = json.loads(response)
            index = result.get('selected_index', 0)

            # 严格验证索引范围
            if isinstance(index, int) and 0 <= index < len(valid_options):
                selected_value = result.get('selected_value', valid_options[index])

                # 验证选择的值是否在选项列表中
                if selected_value in valid_options:
                    actual_index = valid_options.index(selected_value)
                    return {
                        'index': actual_index,
                        'value': selected_value,
                        'confidence': result.get('confidence', 'medium'),
                        'reasoning': result.get('reasoning', 'AI选择')
                    }
                else:
                    # 如果选择的值不在列表中，使用索引对应的值
                    return {
                        'index': index,
                        'value': valid_options[index],
                        'confidence': 'low',
                        'reasoning': '选择值不匹配，使用索引对应值'
                    }
            else:
                logger.warning(f"AI返回的索引{index}无效，使用第一个选项")

        except json.JSONDecodeError:
            logger.warning(f"下拉菜单AI响应无法解析为JSON: {response}")

        # 如果解析失败，尝试从响应中提取数字
        try:
            import re
            numbers = re.findall(r'\d+', response)
            if numbers:
                index = int(numbers[0])
                if 0 <= index < len(valid_options):
                    return {
                        'index': index,
                        'value': valid_options[index],
                        'confidence': 'low',
                        'reasoning': '从非结构化响应中提取索引'
                    }
        except:
            pass

        # 尝试模糊匹配
        try:
            response_lower = response.lower()
            for i, option in enumerate(valid_options):
                if option.lower() in response_lower:
                    return {
                        'index': i,
                        'value': option,
                        'confidence': 'low',
                        'reasoning': '基于文本模糊匹配'
                    }
        except:
            pass

        # 默认返回第一个选项
        return {
            'index': 0,
            'value': valid_options[0],
            'confidence': 'low',
            'reasoning': '默认选择第一个选项'
        }