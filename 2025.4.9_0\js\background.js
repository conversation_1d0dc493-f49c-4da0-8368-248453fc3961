"use strict";import{API_BASE_URL,API_AUTH_URL,API_HISTORY_URL,WEB_DOMAIN,WEB_URL,LOGIN_URL,CAMPUS_URL,HISTORY_URL,AUTOFILL_URL,VERSION_URL,ALL_WEB_URLS}from"./config.js";chrome.runtime.onInstalled.addListener((e=>{ConfigModule.init(),"install"===e.reason&&chrome.tabs.create({url:chrome.runtime.getURL("html/welcome.html")})})),chrome.runtime.onMessage.addListener(((e,a,t)=>{const s={fetchWithJwt:()=>AuthModule.handleMessage(e,a,t),learnField:()=>LearningFieldModule.handleMessage(e,a,t),stopLearnField:()=>LearningFieldModule.handleMessage(e,a,t),addHistory:()=>HistoryModule.handleMessage(e,a,t),clickSource:()=>TabSourceModule.handleMessage(e,a,t),getSource:()=>TabSourceModule.handleMessage(e,a,t),getHistoryUrls:()=>NavigationHistoryModule.handleMessage(e,a,t),logError:()=>ErrorModule.handleMessage(e,a,t)}[e.type];if(s)return s(),!0})),chrome.runtime.onMessageExternal.addListener(((e,a,t)=>("login"===e.type?AuthModule.handleExternalLogin(e,t).catch((e=>t({error:e.message}))):"logout"===e.type&&AuthModule.handleExternalLogout(t).catch((e=>t({error:e.message}))),!0))),chrome.tabs.onCreated.addListener((e=>{TabSourceModule.handleTabCreate(e)})),chrome.tabs.onUpdated.addListener(((e,a,t)=>{})),chrome.tabs.onRemoved.addListener((e=>{LearningFieldModule.handleTabRemove(e),HistoryModule.handleTabRemove(e),TabSourceModule.handleTabRemove(e),NavigationHistoryModule.handleTabRemove(e)})),chrome.webNavigation.onCompleted.addListener((e=>{LearningFieldModule.handleNavigation(e),NavigationHistoryModule.handleNavigation(e),HistoryModule.handleNavigation(e)})),chrome.webNavigation.onHistoryStateUpdated.addListener((e=>{LearningFieldModule.handleNavigation(e),NavigationHistoryModule.handleNavigation(e),HistoryModule.handleNavigation(e)}));const ConfigModule={init(){chrome.storage.local.set({config:{API_BASE_URL:API_BASE_URL,API_AUTH_URL:API_AUTH_URL,API_HISTORY_URL:API_HISTORY_URL,WEB_URL:WEB_URL,LOGIN_URL:LOGIN_URL,CAMPUS_URL:CAMPUS_URL,HISTORY_URL:HISTORY_URL,AUTOFILL_URL:AUTOFILL_URL,VERSION_URL:VERSION_URL,ALL_WEB_URLS:ALL_WEB_URLS}})}},AuthModule={isRefreshing:!1,refreshSubscribers:[],async handleMessage(e,a,t){try{const a=await this.fetchWithJwt(e.url,e.options);t(await a.json())}catch(e){t({error:e.message})}},async handleExternalLogin(e,a){try{const t=e.data.token;await chrome.storage.local.set({auth:{token:t.accessToken,refreshToken:t.refreshToken,userInfo:t.userInfo}}),a({status:"success"})}catch(e){a({status:"error",message:e.message})}},async handleExternalLogout(e){try{await chrome.storage.local.remove(["auth"]),e({status:"success"})}catch(a){e({status:"error",message:a.message})}},async fetchWithJwt(e,a={}){const{auth:t}=await chrome.storage.local.get(["auth"]);if(!t?.token)throw new Error("未登录");const s=async t=>await fetch(e,{...a,headers:{...a.headers,Authorization:`Bearer ${t}`}});try{let e=await s(t.token);if(401===e.status){if(this.isRefreshing)return new Promise(((e,a)=>{this.subscribeTokenRefresh((t=>{s(t).then(e).catch(a)}))}));try{this.isRefreshing=!0;const a=await this.refreshToken();this.onRefreshed(a),e=await s(a)}catch(e){throw this.onRefreshError(),e}finally{this.isRefreshing=!1}}if(!e.ok){const a=await e.json();throw a.detail?new Error(a.detail):new Error("请求失败")}return e}catch(e){throw e}},async refreshToken(){const{auth:e}=await chrome.storage.local.get(["auth"]);if(!e?.refreshToken)throw new Error("未登录");const a=await fetch(`${API_AUTH_URL}refreshToken`,{method:"POST",headers:{Authorization:`Bearer ${e.refreshToken}`}});if(!a.ok)throw new Error("刷新token失败");const t=await a.json();if(!t?.accessToken)throw new Error("刷新token返回数据格式错误");return await chrome.storage.local.set({auth:{...e,token:t.accessToken}}),t.accessToken},subscribeTokenRefresh(e){this.refreshSubscribers.push(e)},onRefreshed(e){this.refreshSubscribers.map((a=>a(e))),this.refreshSubscribers=[]},onRefreshError(){this.refreshSubscribers=[]}},LearningFieldModule={lastSendTime:0,async getLearningTabs(){const{learningTabs:e={}}=await chrome.storage.local.get("learningTabs");return e},async setTab(e,a){const t=await this.getLearningTabs();t[e]=a,await chrome.storage.local.set({learningTabs:t})},async removeTab(e){const a=await this.getLearningTabs();delete a[e],await chrome.storage.local.set({learningTabs:a})},async handleMessage(e,a,t){const s=a.tab.id,r={url:e.url,html:e.html};"learnField"===e.type?(await this.setTab(s,r),t({status:`已收到新的学习数据 ${e.html.length}`})):"stopLearnField"===e.type?(await this.removeTab(s),await this.triggerSaveToServer(r,t)):t({status:"error",message:"未知的消息类型"})},async handleNavigation(e){if(0===e.frameId){const a=(await this.getLearningTabs())[e.tabId];if(!a)return;await this.removeTab(e.tabId),await this.triggerSaveToServer(a)}},async handleTabRemove(e){(await this.getLearningTabs())[e]&&await this.removeTab(e)},async triggerSaveToServer(e,a=null){const t=Date.now();if(t-this.lastSendTime<1e4)return void(a&&a({status:"已忽略回传，距离上次回传不足10秒"}));const s=`${API_BASE_URL}learningField`;await this.saveToServer(s,e),this.lastSendTime=t,a&&a({status:`已保存数据 ${e.html.length}`})},async saveToServer(e,a){try{const t=await AuthModule.fetchWithJwt(e,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});await t.json()}catch(e){}}},HistoryModule={async getHistoryTabs(){const{historyTabs:e={}}=await chrome.storage.local.get("historyTabs");return e},async setTab(e,a){const t=await this.getHistoryTabs();t[e]=a,await chrome.storage.local.set({historyTabs:t})},async removeTab(e){const a=await this.getHistoryTabs();delete a[e],await chrome.storage.local.set({historyTabs:a})},async handleMessage(e,a,t){const s=a.tab.id,r={source:e.source,data:e.data};await this.setTab(s,r),t({status:`已收到新的投递记录数据 ${e.data.campusId||""}`})},async handleNavigation(e){if(0===e.frameId){const a=(await this.getHistoryTabs())[e.tabId];if(!a)return;await this.removeTab(e.tabId);const t=`${API_HISTORY_URL}addHistoryByChrome`,s=await this.saveToServer(t,a);"campus"===a.source&&a.data.campusId&&s.success&&s.data?.historyId&&this.sendHistoryIdToWebsite(a.data.campusId,s.data.historyId)}},async handleTabRemove(e){await this.removeTab(e)},async saveToServer(e,a){try{const t=await AuthModule.fetchWithJwt(e,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});return await t.json()}catch(e){}return null},async sendHistoryIdToWebsite(e,a){try{const t=await chrome.tabs.query({url:`${WEB_DOMAIN}/*`});for(const s of t)chrome.tabs.sendMessage(s.id,{type:"addHistoryCampusId",data:{campusId:e,historyId:a}}).catch((e=>{}))}catch(e){}}},TabSourceModule={pendingSource:null,async getSourceMap(){const{tabSourceMap:e={}}=await chrome.storage.local.get("tabSourceMap");return e},async setSource(e,a){const t=await this.getSourceMap();t[e]=a,await chrome.storage.local.set({tabSourceMap:t})},async removeSource(e){const a=await this.getSourceMap();delete a[e],await chrome.storage.local.set({tabSourceMap:a})},async handleMessage(e,a,t){const s=a.tab.id;if("clickSource"===e.type)this.pendingSource=e.source,t({status:"success"});else if("getSource"===e.type){t({source:(await this.getSourceMap())[s]})}},async handleTabCreate(e){if(e.openerTabId)if(this.pendingSource)await this.setSource(e.id,this.pendingSource),this.pendingSource=null;else{const a=(await this.getSourceMap())[e.openerTabId];a&&await this.setSource(e.id,a)}},async handleTabRemove(e){await this.removeSource(e)}},NavigationHistoryModule={async getHistoryMap(){const{navigationHistory:e={}}=await chrome.storage.local.get("navigationHistory");return e},async setHistory(e,a){const t=await this.getHistoryMap();t[e]=a,await chrome.storage.local.set({navigationHistory:t})},async removeHistory(e){const a=await this.getHistoryMap();delete a[e],await chrome.storage.local.set({navigationHistory:a})},async handleNavigation(e){if(0===e.frameId){let a=(await this.getHistoryMap())[e.tabId]||[];if(a.length&&a[a.length-1]===e.url)return;a.push(e.url),a.length>5&&a.shift(),await this.setHistory(e.tabId,a)}},async handleTabRemove(e){await this.removeHistory(e)},async handleMessage(e,a,t){if("getHistoryUrls"===e.type){t([...(await this.getHistoryMap())[a.tab.id]||[]].reverse())}}},ErrorModule={async handleMessage(e,a,t){try{const{functionName:s,duration:r,errorStack:o,company:i,resumeId:n,browser:c,version:h}=e,l=(a.tab.id,a.tab.url);await this.saveErrorToServer({functionName:s,errorStack:o,duration:r,browser:c,version:h,resumeId:n,company:i,url:l}),t({status:"success"})}catch(e){t({status:"error",message:e.message})}},async saveErrorToServer(e){try{const a=`${API_BASE_URL}logError`,t=await AuthModule.fetchWithJwt(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});return await t.json()}catch(e){return{success:!1,error:e.message}}}};Object.assign(globalThis,{AuthModule:AuthModule,LearningFieldModule:LearningFieldModule,HistoryModule:HistoryModule,TabSourceModule:TabSourceModule,NavigationHistoryModule:NavigationHistoryModule,ErrorModule:ErrorModule});