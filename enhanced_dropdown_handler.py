"""
增强的下拉菜单处理器 - 支持各种复杂的动态下拉菜单
"""

import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys

logger = logging.getLogger(__name__)


class EnhancedDropdownHandler:
    """增强的下拉菜单处理器"""
    
    # 常见的下拉菜单实现方式
    DROPDOWN_PATTERNS = {
        'ant_design': {
            'trigger_selectors': [
                '.ant-select-selector',
                '.ant-select-selection'
            ],
            'option_selectors': [
                '.ant-select-dropdown .ant-select-item',
                '.ant-select-dropdown-menu-item'
            ],
            'active_class': 'ant-select-open'
        },
        'element_ui': {
            'trigger_selectors': [
                '.el-select',
                '.el-input__inner'
            ],
            'option_selectors': [
                '.el-select-dropdown__item',
                '.el-option'
            ],
            'active_class': 'is-focus'
        },
        'bootstrap': {
            'trigger_selectors': [
                '.dropdown-toggle',
                '[data-toggle="dropdown"]'
            ],
            'option_selectors': [
                '.dropdown-menu .dropdown-item',
                '.dropdown-menu li a'
            ],
            'active_class': 'show'
        },
        'material_ui': {
            'trigger_selectors': [
                '.MuiSelect-select',
                '.MuiInputBase-input'
            ],
            'option_selectors': [
                '.MuiMenuItem-root',
                '[role="option"]'
            ],
            'active_class': 'Mui-focused'
        },
        'custom': {
            'trigger_selectors': [
                'input[readonly]',
                'div[role="combobox"]',
                'div[role="button"]',
                '.select-trigger',
                '.custom-select'
            ],
            'option_selectors': [
                '[role="option"]',
                '.option-item',
                '.select-option',
                'ul.options li',
                'div.options div'
            ],
            'active_class': None
        }
    }
    
    def __init__(self, driver, wait_timeout: int = 10):
        self.driver = driver
        self.wait = WebDriverWait(driver, wait_timeout)
        
    def handle_dropdown(self, element, value: str = None, index: int = None) -> bool:
        """处理下拉菜单选择"""
        try:
            # 滚动到元素可见
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)
            
            # 点击下拉菜单打开选项
            logger.info("点击打开下拉菜单...")
            self._click_element_safely(element)
            time.sleep(1)  # 等待下拉菜单展开
            
            # 获取所有可用选项
            options = self._get_dropdown_options(element)
            if not options:
                logger.warning("无法获取下拉菜单选项")
                return False
                
            logger.info(f"找到{len(options)}个下拉菜单选项: {[opt['text'] for opt in options]}")
            
            # 确定要选择的选项
            target_option = None
            if index is not None and 0 <= index < len(options):
                target_option = options[index]
                logger.info(f"按索引{index}选择选项: {target_option['text']}")
            elif value:
                # 按值匹配选项
                for opt in options:
                    if opt['text'] == value or value in opt['text']:
                        target_option = opt
                        logger.info(f"按值匹配选择选项: {target_option['text']}")
                        break
            
            if not target_option:
                logger.warning("未找到匹配的选项")
                return False
                
            # 点击选择选项
            logger.info(f"点击选择选项: {target_option['text']}")
            self._click_element_safely(target_option['element'])
            time.sleep(0.5)
            
            # 验证选择是否生效
            if self._verify_selection(element, target_option['text']):
                logger.info("下拉菜单选择成功")
                return True
            else:
                logger.warning("下拉菜单选择可能未生效，尝试其他方法")
                # 尝试使用键盘选择
                return self._keyboard_select(element, target_option['text'])
                
        except Exception as e:
            logger.error(f"下拉菜单处理失败: {e}")
            return False
            
    def _click_element_safely(self, element):
        """安全地点击元素"""
        try:
            # 先尝试普通点击
            element.click()
        except:
            try:
                # 使用ActionChains点击
                ActionChains(self.driver).move_to_element(element).click().perform()
            except:
                # 使用JavaScript点击
                self.driver.execute_script("arguments[0].click();", element)
                
    def _verify_selection(self, dropdown_element, expected_value: str) -> bool:
        """验证下拉菜单选择是否生效"""
        try:
            time.sleep(0.5)  # 等待DOM更新
            
            # 检查元素的value属性
            current_value = dropdown_element.get_attribute('value')
            if current_value and (current_value == expected_value or expected_value in current_value):
                return True
                
            # 检查元素的文本内容
            current_text = dropdown_element.text
            if current_text and (current_text == expected_value or expected_value in current_text):
                return True
                
            # 检查相关的显示元素
            try:
                parent = dropdown_element.find_element(By.XPATH, '..')
                display_elements = parent.find_elements(By.CSS_SELECTOR, 
                    '.ant-select-selection-item, .el-input__inner, .selected-value, .current-value')
                for elem in display_elements:
                    if elem.text and (elem.text == expected_value or expected_value in elem.text):
                        return True
            except:
                pass
                
            return False
        except:
            return False
            
    def _keyboard_select(self, element, target_value: str) -> bool:
        """使用键盘选择下拉菜单选项"""
        try:
            logger.info("尝试使用键盘选择")
            
            # 确保元素获得焦点
            element.click()
            time.sleep(0.3)
            
            # 发送目标值的首字母
            if target_value:
                first_char = target_value[0]
                element.send_keys(first_char)
                time.sleep(0.5)
                
                # 发送回车确认
                element.send_keys(Keys.ENTER)
                time.sleep(0.5)
                
                # 验证选择
                return self._verify_selection(element, target_value)
                
            return False
        except Exception as e:
            logger.error(f"键盘选择失败: {e}")
            return False
        
    def _find_trigger_element(self, base_element, selectors: List[str]):
        """查找触发器元素"""
        # 首先检查base_element本身
        for selector in selectors:
            if self._matches_selector(base_element, selector):
                return base_element
                
        # 查找子元素
        for selector in selectors:
            try:
                elements = base_element.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    return elements[0]
            except:
                continue
                
        # 查找父元素中的触发器
        try:
            parent = base_element.find_element(By.XPATH, '..')
            for selector in selectors:
                elements = parent.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    return elements[0]
        except:
            pass
            
        return None
        
    def _matches_selector(self, element, selector: str) -> bool:
        """检查元素是否匹配选择器"""
        try:
            # 使用JavaScript检查
            script = """
            var element = arguments[0];
            var selector = arguments[1];
            return element.matches(selector);
            """
            return self.driver.execute_script(script, element, selector)
        except:
            return False
            
    def _open_dropdown(self, trigger_element, active_class: str = None) -> bool:
        """打开下拉菜单"""
        try:
            # 记录初始状态
            initial_options_count = len(self.driver.find_elements(By.CSS_SELECTOR, '[role="option"]'))
            
            # 滚动到元素可见
            self.driver.execute_script("arguments[0].scrollIntoView(true);", trigger_element)
            time.sleep(0.3)
            
            # 尝试点击打开
            try:
                trigger_element.click()
            except:
                # JavaScript点击
                self.driver.execute_script("arguments[0].click();", trigger_element)
                
            # 等待下拉菜单出现
            time.sleep(0.5)
            
            # 检查是否打开
            if active_class:
                # 检查active class
                classes = trigger_element.get_attribute('class') or ''
                if active_class not in classes:
                    # 检查父元素
                    parent = trigger_element.find_element(By.XPATH, '..')
                    parent_classes = parent.get_attribute('class') or ''
                    if active_class not in parent_classes:
                        return False
            else:
                # 检查是否有新的选项出现
                new_options_count = len(self.driver.find_elements(By.CSS_SELECTOR, '[role="option"]'))
                if new_options_count <= initial_options_count:
                    return False
                    
            return True
            
        except Exception as e:
            logger.debug(f"打开下拉菜单失败: {e}")
            return False
            
    def _get_dropdown_options(self, dropdown_element):
        """获取下拉菜单选项"""
        options = []
        
        try:
            # 等待选项加载
            time.sleep(0.5)
            
            # 常见的选项选择器
            option_selectors = [
                # 标准选项
                'option',
                # Ant Design
                '.ant-select-item-option',
                '.ant-select-item',
                '.rc-select-item-option', 
                # Element UI
                '.el-select-dropdown__item',
                '.el-option',
                # Bootstrap
                '.dropdown-item',
                '.dropdown-option',
                # Material UI
                '.MuiMenuItem-root',
                '.MuiListItem-root',
                # 通用
                'li[role="option"]',
                'div[role="option"]',
                '[data-value]',
                '.option',
                '.select-option',
                # 自定义模式
                'li',
                'div.item',
                'div.option-item'
            ]
            
            # 在不同范围内查找选项
            search_contexts = [
                dropdown_element,  # 在下拉菜单元素内查找
                self.driver,       # 在整个页面查找（对于弹出菜单）
            ]
            
            # 尝试在父元素中查找
            try:
                parent = dropdown_element.find_element(By.XPATH, '..')
                search_contexts.append(parent)
            except:
                pass
                
            # 查找下拉菜单容器
            dropdown_containers = []
            try:
                # 查找可能的下拉菜单容器
                containers = self.driver.find_elements(By.CSS_SELECTOR, 
                    '.ant-select-dropdown, .el-select-dropdown, .dropdown-menu, .MuiPaper-root')
                for container in containers:
                    if container.is_displayed():
                        dropdown_containers.append(container)
            except:
                pass
            
            search_contexts.extend(dropdown_containers)
            
            for context in search_contexts:
                for selector in option_selectors:
                    try:
                        elements = context.find_elements(By.CSS_SELECTOR, selector)
                        for elem in elements:
                            if elem.is_displayed() and elem.text.strip():
                                option_text = elem.text.strip()
                                if option_text and option_text not in [opt['text'] for opt in options]:
                                    options.append({
                                        'element': elem,
                                        'text': option_text
                                    })
                    except:
                        continue
                        
                # 如果找到选项就停止搜索
                if options:
                    break
                    
            # 去重并排序
            unique_options = []
            seen_texts = set()
            for opt in options:
                if opt['text'] not in seen_texts:
                    unique_options.append(opt)
                    seen_texts.add(opt['text'])
                    
            logger.info(f"获取到{len(unique_options)}个选项: {[opt['text'] for opt in unique_options]}")
            return unique_options
            
        except Exception as e:
            logger.error(f"获取下拉菜单选项失败: {e}")
            return []
        
    def _select_option(self, options: List[Any], target_value: str, target_index: int) -> bool:
        """选择目标选项"""
        try:
            if target_value:
                # 按文本选择
                for option in options:
                    if option.text.strip() == target_value or target_value in option.text:
                        option.click()
                        logger.info(f"选择了选项: {option.text}")
                        return True
                        
            elif target_index is not None and 0 <= target_index < len(options):
                # 按索引选择
                options[target_index].click()
                logger.info(f"选择了索引{target_index}的选项: {options[target_index].text}")
                return True
                
            # 如果没有指定，选择第一个非空选项
            for option in options:
                if option.text.strip():
                    option.click()
                    logger.info(f"选择了默认选项: {option.text}")
                    return True
                    
        except Exception as e:
            logger.debug(f"选择选项失败: {e}")
            
        return False
        
    def _close_dropdown(self, trigger_element):
        """关闭下拉菜单"""
        try:
            # 点击body关闭
            self.driver.find_element(By.TAG_NAME, 'body').click()
        except:
            pass
            
    def _try_generic_dropdown(self, element, target_value: str, target_index: int) -> bool:
        """尝试通用的下拉菜单处理方法"""
        try:
            # 方法1: 模拟用户输入
            if element.tag_name == 'input' and target_value:
                element.clear()
                element.send_keys(target_value)
                time.sleep(0.5)
                
                # 查找并点击匹配的选项
                possible_options = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{target_value}')]")
                for opt in possible_options:
                    if opt.is_displayed():
                        opt.click()
                        return True
                        
            # 方法2: 查找相邻的下拉按钮
            parent = element.find_element(By.XPATH, '..')
            buttons = parent.find_elements(By.TAG_NAME, 'button')
            for button in buttons:
                if 'dropdown' in button.get_attribute('class') or 'select' in button.get_attribute('class'):
                    button.click()
                    time.sleep(0.5)
                    
                    # 获取选项并选择
                    options = self._get_dropdown_options(self.DROPDOWN_PATTERNS['custom']['option_selectors'])
                    if options:
                        return self._select_option(options, target_value, target_index)
                        
        except Exception as e:
            logger.debug(f"通用方法失败: {e}")
            
        return False
        
    def extract_all_options(self, element) -> List[str]:
        """提取下拉菜单的所有选项"""
        all_options = []
        
        # 尝试每种模式提取选项
        for pattern_name, pattern in self.DROPDOWN_PATTERNS.items():
            try:
                trigger_element = self._find_trigger_element(element, pattern['trigger_selectors'])
                if not trigger_element:
                    continue
                    
                if self._open_dropdown(trigger_element, pattern.get('active_class')):
                    options = self._get_dropdown_options(pattern['option_selectors'])
                    if options:
                        all_options = [opt.text.strip() for opt in options if opt.text.strip()]
                        self._close_dropdown(trigger_element)
                        break
            except:
                continue
                
        return all_options 