"""
自动简历填写工具 - 主程序入口
"""

import logging
import sys
from form_filler import FormFiller
from config import SYSTEM_CONFIG

# 配置日志
logging.basicConfig(
    level=getattr(logging, SYSTEM_CONFIG['log_level']),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)


def main():
    """主函数"""
    print("="*60)
    print("🤖 自动简历填写工具 v2.0")
    print("="*60)
    print("\n✨ 功能说明：")
    print("1. 📄 自动解析本地markdown格式的简历")
    print("2. 👁️ 使用视觉AI识别网页表单（支持全页面截图）")
    print("3. 🤖 智能匹配并填写表单字段")
    print("4. 📝 支持文本框、下拉菜单、日期选择等多种表单类型")
    print("5. 🎓 学习功能：记录您的填写偏好，持续改进")
    print("\n🔧 新增特性：")
    print("• 全页面截图识别，获得更准确的表单分析")
    print("• 结构化AI输出，提供更精准的填写建议")
    print("• 智能学习系统，记录您的修改习惯")
    print("• 优先使用学习到的偏好值")
    print("\n⚠️ 注意事项：")
    print("- 请确保已配置好API密钥")
    print("- 需要Chrome/Edge/Firefox浏览器")
    print("- 请在浏览器中登录招聘网站")
    print("- 填写完成后，请检查并修改结果，系统会学习您的偏好")
    print("="*60)
    
    try:
        # 创建表单填写器实例
        filler = FormFiller()
        
        # 运行自动填写
        filler.run()
        
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        print("\n⏹️ 操作已取消")
    except Exception as e:
        logger.error(f"程序出错: {e}")
        print(f"\n❌ 程序出错: {e}")
        print("请检查错误信息并重试")
        sys.exit(1)


if __name__ == "__main__":
    main() 