"use strict";import{WEB_URL,LOGIN_URL}from"./config.js";document.addEventListener("DOMContentLoaded",(async()=>{const e=document.querySelector("#arcButtonShow"),t=document.querySelector("#arcButtonHidden"),o=document.querySelector("#arcButtonAuto"),c=document.querySelector("#beautifyButton"),n=document.querySelector("#learningButton"),a=document.querySelector("#highlightButton"),r=await chrome.storage.local.get(["arcButtonMode","showArc","beautifyResume","learningResume","highlightEnabled"]);r.arcButtonMode?"show"===r.arcButtonMode?e.checked=!0:"hidden"===r.arcButtonMode?t.checked=!0:o.checked=!0:(!1===r.showArc?t.checked=!0:o.checked=!0,chrome.storage.local.set({arcButtonMode:!1===r.showArc?"hidden":"auto"})),c.checked=!0===r.beautifyResume,n.checked=!1!==r.learningResume,a.checked=!1!==r.highlightEnabled,e.addEventListener("change",(()=>{e.checked&&(chrome.storage.local.set({arcButtonMode:"show"}),toggleArcButtonMode("show"))})),t.addEventListener("change",(()=>{t.checked&&(chrome.storage.local.set({arcButtonMode:"hidden"}),toggleArcButtonMode("hidden"))})),o.addEventListener("change",(()=>{o.checked&&(chrome.storage.local.set({arcButtonMode:"auto"}),toggleArcButtonMode("auto"))})),c.addEventListener("change",(()=>{chrome.storage.local.set({beautifyResume:c.checked}),toggleBeautifyResume(c.checked)})),n.addEventListener("change",(()=>{chrome.storage.local.set({learningResume:n.checked})})),a.addEventListener("change",(()=>{chrome.storage.local.set({highlightEnabled:a.checked}),toggleHighlight(a.checked)})),chrome.storage.local.get(["websiteCount","fieldCount"],(function(e){document.querySelector("#websiteCount").textContent=e.websiteCount||"0",document.querySelector("#fieldCount").textContent=e.fieldCount||"0"}));document.querySelector("#visitArc").addEventListener("click",(()=>{chrome.tabs.create({url:WEB_URL})}));document.querySelector("#logout").addEventListener("click",(async()=>{await chrome.storage.local.remove(["auth"]),s=null,d()}));let{auth:s}=await chrome.storage.local.get(["auth"]);const l=document.querySelector(".profile"),d=()=>{document.querySelector(".profile-name").textContent="点击登录";document.querySelector("#logout").style.display="none"};s?.userInfo?(document.querySelector(".profile-name").textContent="求职方舟 · "+s.userInfo.nickname,document.querySelector("#logout").style.display="block"):d(),l.addEventListener("click",(async()=>{s?.userInfo?chrome.tabs.create({url:WEB_URL}):chrome.tabs.create({url:LOGIN_URL})}))}));const toggleArcButtonMode=async e=>{sendMessageToAllTabs("toggleArcButtonMode",e)},toggleHighlight=async e=>{sendMessageToAllTabs("toggleHighlight",e)},toggleBeautifyResume=async e=>{sendMessageToAllTabs("toggleBeautifyResume",e)},sendMessageToAllTabs=async(e,t)=>{try{const o=(await chrome.tabs.query({})).map((o=>chrome.tabs.sendMessage(o.id,{action:e,tabId:o.id,state:t}).catch((e=>{}))));await Promise.allSettled(o)}catch(e){e.message.includes("Could not establish connection")?statusMessage.textContent="无法连接到页面，请刷新页面后重试。":statusMessage.textContent=`开启插件时出错: ${e.message}`}};