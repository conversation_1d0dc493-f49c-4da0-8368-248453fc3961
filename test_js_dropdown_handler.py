#!/usr/bin/env python3
"""
测试JavaScript方式的下拉菜单处理器
"""

import logging
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from dropdown_handler_js import JavaScriptDropdownHandler

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_js_dropdown_handler():
    """测试JavaScript下拉菜单处理器"""
    driver = None
    
    try:
        # 设置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        # 创建WebDriver
        driver = webdriver.Chrome(options=chrome_options)
        logger.info("浏览器启动成功")
        
        # 创建测试HTML页面
        test_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>下拉菜单测试页面</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .form-group { margin: 20px 0; }
                label { display: block; margin-bottom: 5px; font-weight: bold; }
                select, input { padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
                .custom-select { 
                    position: relative; 
                    display: inline-block; 
                    cursor: pointer;
                    padding: 8px;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    background: white;
                    min-width: 200px;
                }
                .dropdown-menu { 
                    display: none; 
                    position: absolute; 
                    top: 100%; 
                    left: 0; 
                    right: 0;
                    background: white; 
                    border: 1px solid #ccc; 
                    border-radius: 4px;
                    z-index: 1000;
                }
                .dropdown-menu.show { display: block; }
                .dropdown-item { 
                    padding: 8px 12px; 
                    cursor: pointer; 
                    border-bottom: 1px solid #eee;
                }
                .dropdown-item:hover { background: #f5f5f5; }
                .dropdown-item:last-child { border-bottom: none; }
            </style>
        </head>
        <body>
            <h1>下拉菜单测试页面</h1>
            
            <div class="form-group">
                <label for="education">学历（标准select）:</label>
                <select id="education" name="education">
                    <option value="">请选择学历</option>
                    <option value="high_school">高中</option>
                    <option value="college">专科</option>
                    <option value="bachelor">本科</option>
                    <option value="master">硕士</option>
                    <option value="doctor">博士</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="gender">性别（自定义下拉菜单）:</label>
                <div class="custom-select" id="gender" data-value="">
                    <span class="selected-text">请选择性别</span>
                    <div class="dropdown-menu">
                        <div class="dropdown-item" data-value="male">男</div>
                        <div class="dropdown-item" data-value="female">女</div>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="experience">工作经验（自定义下拉菜单）:</label>
                <div class="custom-select" id="experience" data-value="">
                    <span class="selected-text">请选择工作经验</span>
                    <div class="dropdown-menu">
                        <div class="dropdown-item" data-value="fresh">应届生</div>
                        <div class="dropdown-item" data-value="1-3">1-3年</div>
                        <div class="dropdown-item" data-value="3-5">3-5年</div>
                        <div class="dropdown-item" data-value="5+">5年以上</div>
                    </div>
                </div>
            </div>
            
            <script>
                // 自定义下拉菜单交互逻辑
                document.querySelectorAll('.custom-select').forEach(select => {
                    const selectedText = select.querySelector('.selected-text');
                    const dropdownMenu = select.querySelector('.dropdown-menu');
                    const items = select.querySelectorAll('.dropdown-item');
                    
                    // 点击选择器时切换下拉菜单
                    select.addEventListener('click', (e) => {
                        e.stopPropagation();
                        // 关闭其他下拉菜单
                        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                            if (menu !== dropdownMenu) {
                                menu.classList.remove('show');
                            }
                        });
                        // 切换当前下拉菜单
                        dropdownMenu.classList.toggle('show');
                    });
                    
                    // 点击选项时选择
                    items.forEach(item => {
                        item.addEventListener('click', (e) => {
                            e.stopPropagation();
                            const value = item.getAttribute('data-value');
                            const text = item.textContent;
                            
                            selectedText.textContent = text;
                            select.setAttribute('data-value', value);
                            dropdownMenu.classList.remove('show');
                            
                            // 触发change事件
                            const changeEvent = new Event('change', { bubbles: true });
                            select.dispatchEvent(changeEvent);
                        });
                    });
                });
                
                // 点击页面其他地方时关闭下拉菜单
                document.addEventListener('click', () => {
                    document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                        menu.classList.remove('show');
                    });
                });
            </script>
        </body>
        </html>
        """
        
        # 加载测试页面
        driver.get("data:text/html;charset=utf-8," + test_html)
        time.sleep(2)
        logger.info("测试页面加载完成")
        
        # 创建JavaScript下拉菜单处理器
        js_handler = JavaScriptDropdownHandler(driver)
        logger.info("JavaScript下拉菜单处理器创建完成")
        
        # 测试1: 探索所有下拉菜单选项
        logger.info("="*60)
        logger.info("测试1: 探索所有下拉菜单选项")
        logger.info("="*60)
        
        js_handler.explore_all_dropdowns()
        dropdown_data = js_handler.get_all_dropdown_data()
        
        logger.info("探索结果:")
        logger.info(f"  input类型: {dropdown_data['input_doms']}个")
        logger.info(f"  select类型: {dropdown_data['select_doms']}个")
        logger.info(f"  radio类型: {dropdown_data['radio_doms']}个")
        
        for i, items in enumerate(dropdown_data['input_items']):
            if items:
                logger.info(f"  input[{i}]选项: {items}")
                
        for i, items in enumerate(dropdown_data['select_items']):
            if items:
                logger.info(f"  select[{i}]选项: {items}")
                
        for i, items in enumerate(dropdown_data['radio_items']):
            if items:
                logger.info(f"  radio[{i}]选项: {items}")
        
        # 测试2: 选择下拉菜单选项
        logger.info("="*60)
        logger.info("测试2: 选择下拉菜单选项")
        logger.info("="*60)
        
        # 测试标准select
        if js_handler.select_doms:
            education_select = js_handler.select_doms[0]
            success = js_handler.click_select_option(education_select, "本科", ["请选择学历", "高中", "专科", "本科", "硕士", "博士"])
            logger.info(f"选择学历'本科': {'成功' if success else '失败'}")
            time.sleep(1)
        
        # 测试自定义下拉菜单
        if js_handler.input_doms:
            for i, element in enumerate(js_handler.input_doms):
                if i < len(dropdown_data['input_items']) and dropdown_data['input_items'][i]:
                    options = dropdown_data['input_items'][i]
                    if len(options) > 1:
                        target_option = options[1]  # 选择第二个选项
                        success = js_handler.click_select_option(element, target_option, options)
                        logger.info(f"选择自定义下拉菜单选项'{target_option}': {'成功' if success else '失败'}")
                        time.sleep(1)
                        break
        
        # 等待一段时间观察结果
        logger.info("等待5秒观察结果...")
        time.sleep(5)
        
        logger.info("✅ JavaScript下拉菜单处理器测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if driver:
            driver.quit()
            logger.info("浏览器已关闭")

if __name__ == "__main__":
    print("🚀 开始测试JavaScript下拉菜单处理器")
    test_js_dropdown_handler()
    print("✅ 测试完成")
