"use strict";window.config=null,window.runFillResume=null;let beautifyResumeResponse=null,fieldList=[],fieldResponse=[],fieldItems=[],resumeValueJson=[],formatValueJson=[],inputDoms=[],inputItems=[],selectDoms=[],selectItems=[],radioDoms=[],radioItems=[];function initParams(){beautifyResumeResponse=null,fieldList=[],fieldResponse=[],fieldItems=[],resumeValueJson=[],formatValueJson=[],inputDoms=[],inputItems=[],selectDoms=[],selectItems=[],radioDoms=[],radioItems=[],isHttpResponseError=!1,httpResponseErrorAct="",_blockRemoveDoms=[],_isGetModalFinished=!1,_isHighlightFinished=!1,_domObserver=null,_observeAddDoms=[],_oldComputedStyles=new WeakMap,_modalCancelDoms=[],_modalSubmitDoms=[],_modalDeleteDoms=[],learningInterval&&(clearInterval(learningInterval),learningInterval=null,learningHtml="",_stopLearningToBackground())}async function fixBlockAddButton(){const e=_documentQuerySelectorAll(),t=[];for(const o of e){const e=o.textContent.trim();/^[\+ ]*[添增]加/.test(e)&&!/职位/.test(e)&&t.push(o)}const o=_filterDomsLeaveChildren(t);for(const e of o){e.scrollIntoView({block:"center"}),await waitTime(100),_observeDomChanges(),await _clickDom(e),await waitTime(200),_stopObserveDomChanges();const t=_getNewBlockDoms();if(0==t.length)continue;_hasSameDomBefore(t)&&await _deleteBlockDom(t)}await waitTime(100)}function _getNewBlockDoms(){const e=_filterDomsLeaveParent(_observeAddDoms),t=[];for(const o of e){if(!_isDomVisible(o))continue;const e=o.textContent.trim();/[\u4e00-\u9fa5]/.test(e)&&t.push(o)}_blockRemoveDoms=[];for(const e of t)_checkBlockRemoveBtn(e);return _clearObserveDoms(),t}window.runFillResume=async(e,t,o,n,i,s=e=>{})=>{let l=new Date;try{window.setStateText("方舟！启动！"),initParams(),await fixBlockAddButton();for(findFieldByServer(cleanHtml()),i&&beautifyResumeByServer(e,t,o),getBlankModalItem(),window.setStateText("正在扫描网站...","min");!_isGetModalFinished||!window.isRunning();)_checkResponseError(),await waitTime(500);for(window.setStateText("尝试理解网站，请稍候...","min");!fieldResponse.length||!window.isRunning();)_checkResponseError(),await waitTime(500);if(window.setStateText("正在标记简历字段...","min"),fieldList=handleFieldResponse(fieldResponse),0==fieldList.length)throw new Error("服务器返回错误");if(findFieldDoms(fieldList),findBlankDoms(fieldList),fieldItems=addItemsIntoFieldResponse(fieldList,fieldResponse),i){let i=setInterval((()=>{if(beautifyResumeResponse){if(!(o=beautifyResumeResponse.resumeMd))throw new Error("美化简历生成错误");if(!(n=beautifyResumeResponse.resumeId))throw new Error("美化简历生成错误");fillResumeByServer(fieldItems,e,t,o,n),i&&(clearInterval(i),i=null)}}),500)}else fillResumeByServer(fieldItems,e,t,o,n);for(highlightField(fieldList);!_isHighlightFinished||!window.isRunning();)_checkResponseError(),await waitTime(500);if(i){for(window.setStateText("上网搜索该公司的岗位要求...","show"),await window.breatheJobInfo(),window.setStateText("针对岗位，生成专岗美化简历..."),window.breatheResume("begin");!beautifyResumeResponse;)_checkResponseError(),await waitTime(500);for(window.breatheResume("end");!window.isRunning();)_checkResponseError(),await waitTime(500);window.setStateText("专岗简历生成中..."),window.bindBeautifyResume(beautifyResumeResponse);let e=0;for(;!resumeValueJson.length||!window.isRunning();)_checkResponseError(),await waitTime(500),e++,22==e?window.setStateText("开始思考网站填写策略..."):60==e?window.setStateText("思考时间稍长，请耐心等候..."):90==e&&window.setStateText("快要完成了，我在努力中...");window.stopTypeResumeContent(o)}else{window.setStateText("正在理解简历...","show"),window.breatheResume("begin");let e=0;for(;!resumeValueJson.length||!window.isRunning();)_checkResponseError(),await waitTime(500),e++,16==e?(window.breatheResume("end"),window.setStateText("开始思考网站填写策略...")):60==e?window.setStateText("思考时间稍长，请耐心等候..."):90==e&&window.setStateText("快要完成了，我在努力中...");window.breatheResume("end")}window.setStateText("尝试为你填写简历...","min"),await waitTime(500),formatValueJson=formatResumeValueJson(resumeValueJson),await fillBlankDoms(fieldList,formatValueJson),window.setStateText("填写完成！剩下的空就交给你咯~","show"),s({status:"success"}),await addStatistics(),startLearningField(),await addHistory(e,t),window.closeHighlight()}catch(t){const o=t.stack||"";let i="未知函数";if(isHttpResponseError)i=httpResponseErrorAct;else{const e=o.split("\n");for(let t=1;t<e.length;t++){const o=e[t].trim();if(!o.includes("at new Error")&&!o.includes("at catch")){const e=o.match(/at\s+([^\s(]+)|at\s+[^(]*\(([^)]*)\)/);if(e){if(i=e[1]||e[2]||"未知函数",i=i.split("/").pop().split(":")[0],i.length<=3)continue;break}}}}window.setStateText("填写出错！我不行了，靠你咯...","show"),s({status:"error",error:t});try{logErrorToServer(i,String(o),l,e,n)}catch(e){}}return!0};let _blockRemoveDoms=[];function _checkBlockRemoveBtn(e){const t=e.querySelectorAll("*");for(const e of t){let t=!1;if(!t&&e.innerText){const o=e.innerText.trim();/^(删除|移除).{0,4}$/.test(o)&&(t=!0)}t&&_blockRemoveDoms.push(e)}for(const e of t){let t=!1;const o=e.attributes;for(const e of o){const o=e.value;if(/(?:^|\W+)(shanchu|(del|delete|remove)(|btn|button)|删除|移除)(?:\W+|$)/i.test(o)){t=!0;break}}t&&_blockRemoveDoms.push(e)}}function _hasSameDomBefore(e){let t=[];for(const o of e){t.push(o);const e=Array.from(o.querySelectorAll("*"));t=t.concat(e)}t=t.reverse();const o=e[0],n=_documentQuerySelectorAll().reverse();let i=0,s=!1;for(let e=0;e<n.length;e++){const l=n[e];if(l===o){s=!0;continue}if(!s)continue;if(i>=t.length)return!0;const a=t[i];if(l.nodeType===a.nodeType&&l.tagName===a.tagName&&l.className===a.className){const e=Array.from(l.attributes).map((e=>e.name)).sort(),t=Array.from(a.attributes).map((e=>e.name)).sort();if(JSON.stringify(e)===JSON.stringify(t)){i++;continue}}if(l.nodeType!==a.nodeType||l.tagName!==a.tagName||"object"!=typeof l.className||"object"!=typeof a.className){if(l.nodeType===a.nodeType&&l.tagName===a.tagName&&"string"==typeof l.className&&"string"==typeof a.className&&l.className.length===a.className.length){const e=/[^a-zA-Z0-9]/g;let t=[...l.className.matchAll(e)],o=[...a.className.matchAll(e)];const n=t.length===o.length,s=t.every(((e,t)=>{const n=o[t];return e.index===n.index&&e[0]===n[0]})),r=n&&s,c=Array.from(l.attributes).map((e=>e.name)).sort(),m=Array.from(a.attributes).map((e=>e.name)).sort(),d=JSON.stringify(c)===JSON.stringify(m);if(r&&d&&""!==l.innerText&&""!==a.innerText){if(l.innerText===a.innerText){i++;continue}{const e=Math.max(l.innerHTML.length,a.innerHTML.length),t=Math.min(l.innerHTML.length,a.innerHTML.length);if(e>100&&t/e>.8){if(_isDomSimilar(l,a)){i++;continue}}}}}if(l.innerText&&!/^[\+\- ]*(添加|增加|删除|移除|收起|展开)/.test(l.innerText)){if(a.innerText&&!/^[\+\- ]*(添加|增加|删除|移除|收起|展开)/.test(a.innerText))break;i++,e--}}else i++}return!1}function _isDomSimilar(e,t){return _calculateTextSimilarity(e.innerHTML,t.innerHTML)>80}function _calculateTextSimilarity(e,t){e=e.replace(/\s+/g,""),t=t.replace(/\s+/g,"");Math.max(e.length,t.length);if(0===Math.min(e.length,t.length))return 0;if(e===t)return 100;const o=e.match(new RegExp(".{1,1000}","g"))||[],n=t.match(new RegExp(".{1,1000}","g"))||[];let i=0;const s=Math.max(o.length,n.length);for(let e=0;e<s;e++){const t=o[e]||"",s=n[e]||"";let l=0;const a=Math.min(t.length,s.length);for(let e=0;e<a;e++)t[e]===s[e]&&l++;i+=l}return 2*i/(e.length+t.length)*100}async function _deleteBlockDom(e){const t=_filterDomsLeaveChildren(_blockRemoveDoms);t.reverse();for(const o of t){_observeDomChanges(),await _clickDom(o),await waitTime(100),_stopObserveDomChanges();const t=_filterDomsLeaveParent(_observeAddDoms);for(const e of t)_getModalDeleteDoms(e),await _clickDeleteModal();_clearObserveDoms(),await waitTime(100);let n=!0;for(const t of e)if(_isDomVisible(t)){n=!1;break}if(n)return}}function _handleObserveModal(){const e=_filterDomsLeaveParent(_observeAddDoms);let t=[];for(const o of e){const e=_getModalItems(o);e.length>=2&&(t=e),_getModalCancelDoms(o)}return t}function cleanHtml(){const{clone:e,stylesMap:t}=_cloneWithStyles(document.body),o=e;let n=[];const i=["ark-ai"];for(const e of i){const t=o.querySelector(`#${e}`);t&&t.parentNode&&t.remove()}n=o.querySelectorAll("*");for(const e of n)_isCloneDomVisible(e,t)||e.remove();const s=e=>{const t=e.childNodes;for(let e=t.length-1;e>=0;e--){const o=t[e];o.nodeType===Node.COMMENT_NODE||o.nodeType===Node.TEXT_NODE&&""===o.nodeValue.trim()?o.remove():o.nodeType===Node.ELEMENT_NODE&&s(o)}};s(o),n=o.querySelectorAll("noscript, script, link, style, img, canvas, svg");for(const e of n)e.remove();const l=o.querySelectorAll("*");for(const e of l)if(e.childNodes.length>0)for(const t of e.childNodes)t.nodeType===Node.TEXT_NODE&&(t.nodeValue=t.nodeValue.replace(/\n\s*\n+/g," ").trim());n=o.querySelectorAll("input, textarea, select");for(const e of n)if(e.value){const t=document.createElement("span");t.textContent=e.value,e.parentNode.replaceChild(t,e)}let a=!0;for(;a;){a=!1,n=o.querySelectorAll("*");for(const e of n)""===e.innerText.trim()&&(e.remove(),a=!0)}n=o.querySelectorAll("*");for(const e of n)for(const t of Array.from(e.attributes))e.removeAttribute(t.name);return o.innerHTML.trim()}function _cloneWithStyles(e){const t=e.cloneNode(!0),o=e.querySelectorAll("*"),n=t.querySelectorAll("*"),i=new Map;let s=0;for(const e of o){const t=getComputedStyle(e),o={display:t.display,visibility:t.visibility,opacity:t.opacity};i.set(n[s],o),s++}return{clone:t,stylesMap:i}}function _isCloneDomVisible(e,t){const o=t.get(e);return!o||"none"!==o.display&&"hidden"!==o.visibility&&"0"!==o.opacity&&!e.hidden}async function findFieldByServer(e){try{const t=await fetchWithJwt(`${window.config.API_BASE_URL}getNeedField`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:window.location.href,html:e})});fieldResponse=t.fields}catch(e){throw isHttpResponseError=!0,httpResponseErrorAct="getNeedField",e}return null}async function beautifyResumeByServer(e,t,o){try{beautifyResumeResponse=await fetchWithJwt(`${window.config.API_BASE_URL}beautifyResumeMd`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:window.location.href,company:e,position:t,resumeMd:o})})}catch(e){throw isHttpResponseError=!0,httpResponseErrorAct="beautifyResumeMd",e}return null}let _isGetModalFinished=!1;async function getBlankModalItem(){_isGetModalFinished=!1,window.scrollTo({top:0,left:0,behavior:"instant"}),await waitTime(10);const e=_getCertainInputDoms(_documentQuerySelectorAll());inputDoms=e.inputDoms,selectDoms=e.selectDoms,radioDoms=e.radioDoms;const t=_getBorderHeightList(inputDoms);inputDoms=_getPossibleInputDoms(inputDoms,selectDoms,t,_documentQuerySelectorAll()),inputDoms=_uniqueSamePositionInputDoms(inputDoms),inputItems=[];for(const e of inputDoms){const t=e.style.backgroundColor;e.style.backgroundColor="var(--highlight-yellow)",e.scrollIntoViewIfNeeded(),await waitTime(200),_observeDomChanges(),await _clickDom(e),await waitTime(100),_observeAddDoms.length>0&&await waitTime(200),_checkNewModalDom()&&await waitTime(300),_stopObserveDomChanges();const o=_handleObserveModal();for(await _clickCloseModal(e),e.style.backgroundColor=t,inputItems.push(o),await _blurDom(e),o.length&&await waitTime(200),_clearObserveDoms();!window.isRunning();)await waitTime(500)}const o=[];for(const e of inputItems)e.length>0&&/^[-\s]*请选择/.test(e[0])?o.push(e.slice(1)):o.push(e);inputItems=o,selectItems=[];for(const e of selectDoms){const t=_getSelectItems(e);t.length>=2?/^[-\s]*请选择/.test(t[0])?selectItems.push(t.slice(1)):selectItems.push(t):selectItems.push([])}radioItems=[];for(const e of radioDoms){const t=_getModalItems(e);t.length>=2?radioItems.push(t):radioItems.push([])}_isGetModalFinished=!0}function _getCertainInputDoms(e){let t=[],o=[],n=[];for(const i of e){let e=!1,s=!1,l=!1;if(i.getBoundingClientRect().bottom<=150)continue;const a=i.getAttribute("placeholder"),r=i.getAttribute("title");if(a&&"TEXTAREA"!=i.tagName&&!/^\s*(搜索|查找)/.test(a)&&!/^\s*(搜索|查找)/.test(r)||i.classList.contains("ant-select")?e=!0:"INPUT"!==i.tagName||/^\s*(搜索|查找)/.test(a)||/^\s*(搜索|查找)/.test(r)||!["text","search"].includes(i.type)?"SELECT"===i.tagName?s=!0:_isRadioDom(i)&&(l=!0):e=!0,e||s||l)if(_isDomVisible(i))e?t.push(i):s?o.push(i):n.push(i);else{let o=i.parentElement;for(;o&&!_isDomVisible(o);)o=o.parentElement;o&&(e?t.push(o):s||n.push(o))}}return t=_filterDomsLeaveChildren(t),o=_filterDomsLeaveChildren(o),n=_filterDomsLeaveParent(n),{inputDoms:t,selectDoms:o,radioDoms:n}}function _getBorderHeightList(e){let t=[];for(const o of e){let e=o;for(;e&&e.offsetHeight<40&&e!==document.body;){const o=getComputedStyle(e).borderBottom;if(o&&!o.includes("none")&&!o.includes("hidden")&&!/\b0(\s|$|\w)/.test(o)&&!o.includes("transparent")){for(let n=-2;n<=2;n++){const i=`${o}_${e.offsetHeight+n}`;t.includes(i)||t.push(i)}break}e=e.parentElement}}return t}function _getPossibleInputDoms(e,t,o,n){let i=[...e],s=null;for(const e of n){if(i.includes(e)){s=e;continue}if(t.includes(e)){s=e;continue}if(s&&s.contains(e))continue;if(e.children.length>0)continue;if(e.getBoundingClientRect().bottom<=100)continue;const n=e.innerHTML.trim();if(!n||!/[\u4e00-\u9fa5]/.test(n))continue;const l=n.replace(/\s/g,"");if(/^(确定|取消|返回|关闭|提交|报名|投递|预览|保存|暂存|[上下]一步|编辑|\+?[添增]加|删除|移除|收起|展开|点击|(简历)?上传|立即)\s*/.test(l))continue;let a=e;for(;a&&a.offsetHeight<=50&&a!==document.body;){const e=`${getComputedStyle(a).borderBottom}_${a.offsetHeight}`;if(o.includes(e)&&_isDomVisible(a)){i.push(a),s=a;break}a=a.parentElement}}return i=_filterDomsLeaveChildren(i),i.sort(((e,t)=>e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING?-1:1)),i}function _uniqueSamePositionInputDoms(e){let t=[],o=null;for(const n of e){if(!o){o=n,t.push(n);continue}const e=n.getBoundingClientRect(),i=o.getBoundingClientRect(),s=e.left+e.width/2,l=e.top+e.height/2,a=i.left+i.width/2,r=i.top+i.height/2;if(s>=i.left&&s<=i.right&&l>=i.top&&l<=i.bottom||a>=e.left&&a<=e.right&&r>=e.top&&r<=e.bottom){const e=_getInputDomText(n);_getInputDomText(o).length>0||e.length>0&&(t.pop(),t.push(n),o=n)}else t.push(n),o=n}return t}function _getInputDomText(e){function t(e){let t=e.trim();return t=t.replace(/^请?(选择|输入|填写|填入)/g,""),t}const o=[e,...e.querySelectorAll("*")];for(const e of o){const o=e.getAttribute("placeholder")?.trim();if(o)return t(o)}for(const e of o)if(0===e.children.length){const o=e.textContent.trim();if(o&&/[\u4e00-\u9fa5]/.test(o))return t(o)}return""}function _checkNewModalDom(){if(0==_observeAddDoms.length)return!1;let e=!1;for(const t of[..._observeAddDoms]){let o=t,n=!1;for(;1===o.children.length;){const e=o.children[0];if("svg"===e.tagName.toLowerCase()){n=!0,_observeAddDoms.splice(_observeAddDoms.indexOf(t),1);break}o=e}n||(e=!0)}if(!e)return!1;for(const e of _observeAddDoms)if(_isDomVisible(e)&&_isInViewport(e))return!0;return!1}function _isInViewport(e){const t=e.getBoundingClientRect();if(t.x>=0&&t.y>=0&&t.right<=window.innerWidth&&t.bottom<=window.innerHeight)return!0;for(const t of e.children)if(_isInViewport(t))return!0;return!1}function _handleObserveModal(){const e=_filterDomsLeaveParent(_observeAddDoms);let t=[];for(const o of e){const e=_getModalItems(o);e.length>=2&&(t=e),_getModalCancelDoms(o)}return t}function handleFieldResponse(e){const t=[];try{if(0==e.length)throw new Error("服务器返回错误");for(const o of e){const e={name:o.name,dom:null,fields:[]};t.push(e);for(const t of o.fields){const o={name:t.name,field:{dom:null},blanks:[]};e.fields.push(o)}}}catch(e){}return t}function findFieldDoms(e){try{const t=e.map((e=>e.name)),o=_documentQuerySelectorAll();let n=null;for(const e of o){const o=e.childNodes;if(o.length<5)continue;let i=0;for(const e of o){const o=e.textContent.trim();if(o&&t.includes(o))i++;else if(o.length>10){i=0;break}}if(i>=5){n=e;break}}let i=null;for(const t of e){t.dom=_findOneDom(t.name,i,n),i=t.dom;for(const e of t.fields)e.field.dom=_findOneDom(e.name,i,n),i=e.field.dom}}catch(e){}}function _findOneDom(e,t,o){try{const n=_documentQuerySelectorAll();let i=!t;for(const s of n){if(o&&o.contains(s))continue;if(s===t){i=!0;continue}if(!i)continue;if("OPTION"===s.tagName)continue;let n=e.trim();n=n.replace(/[.*+?^${}()|\[\]\\]/g,"\\$&");const l=new RegExp(`^[\\s\\*]*${n}[\\s\\*]*(:|：)?[\\s\\*]*$`);if(l.test(s.textContent))return _findMinDomInDom(s,l)}}catch(e){}return null}function findBlankDoms(e){try{const t=[],o=[];for(const n of e)for(const e of n.fields)e.field.dom&&(t.push(e.field.dom),o.push([]));const n=_documentQuerySelectorAll();let i=null,s=0,l=null,a=null,r=[];for(const e of n){if(t.includes(e)){if(!o[s].length&&r.length){for(const e of r){let t=e.parentElement;for(;t&&!_isDomVisible(t);)t=t.parentElement;t&&o[s].push(t)}r=[]}i=e,s=t.indexOf(e),l=s+1<t.length?t[s+1]:null;continue}if(!i)continue;if(a&&a.contains(e))continue;if(l&&e.contains(l))continue;let n=!1;("INPUT"===e.tagName&&["text","search","number"].includes(e.type)||"TEXTAREA"===e.tagName||"SELECT"===e.tagName||e.isContentEditable)&&(n=!0),inputDoms.includes(e)&&(n=!0),n?_isDomVisible(e)?o[s].push(e):r.push(e):(selectDoms.includes(e)&&(n=!0,o[s].push(e)),radioDoms.includes(e)&&(n=!0,o[s].push(e)))}for(const e in o)o[e]=_filterDomsLeaveChildren(o[e]);const c=[];for(let e=0;e<t.length;e++){t[e];const n=o[e];if(c[e]=[],0!=n.length)if(1==n.length)c[e].push(n[0]);else{for(const t of n)if(_isRadioDom(t)){c[e].push(t);break}if(c[e].length)continue;c[e]=n}}for(let o=0;o<c.length;o++)if(0!=c[o].length)for(const n of e)for(const e of n.fields)if(e.field.dom===t[o])if(1===c[o].length){c[o][0];e.blanks.push({name:"",dom:c[o][0],type:radioDoms.includes(c[o][0])?"radio":selectDoms.includes(c[o][0])?"select":"input"})}else for(const t of c[o])e.blanks.push({name:_getInputDomText(t),dom:t,type:radioDoms.includes(t)?"radio":selectDoms.includes(t)?"select":"input"})}catch(e){}}function addItemsIntoFieldResponse(e,t){try{const o=[];for(const e of t)o.push({name:e.name,fields:[]});for(let t=0;t<e.length;t++){const n=e[t],i=[];o[t].fields=i;for(let e=0;e<n.fields.length;e++){const t=n.fields[e];if(0==t.blanks.length||1==t.blanks.length&&/时间|日期|年月/.test(t.name)){const e={name:t.name};i.push(e)}else for(const e of t.blanks){const o={name:1==t.blanks.length?t.name:`${t.name} - ${e.name}`};i.push(o);let n=-1;n=inputDoms.indexOf(e.dom),-1!==n&&inputItems[n]&&inputItems[n].length>0?o.items=inputItems[n].slice(0,1e3):(n=selectDoms.indexOf(e.dom),-1!==n&&selectItems[n]&&selectItems[n].length>0?o.items=selectItems[n]:(n=radioDoms.indexOf(e.dom),-1!==n&&radioItems[n]&&radioItems[n].length>0&&(o.items=radioItems[n])))}}}return o}catch(e){return null}}let _domObserver,_isHighlightFinished=!1;async function highlightField(e){_isHighlightFinished=!1;try{window.scrollTo({top:0,left:0,behavior:"instant"});for(const t of e)if(null!=t.dom){t.dom.scrollIntoView({block:"center"}),t.dom.style.backgroundColor="var(--highlight-purple)",await waitTime(100);for(const e of t.fields)if(null!=e.field.dom){e.field.dom.scrollIntoView({block:"center"}),e.field.dom.style.backgroundColor="var(--highlight-yellow)";for(const t of e.blanks)t.dom.style.backgroundColor="var(--highlight-green)",await waitTime(100)}}scrollTo(0,0)}catch(e){}_isHighlightFinished=!0}async function fillResumeByServer(e,t,o,n,i){try{const s=await fetchWithJwt(`${window.config.API_BASE_URL}fillResumeValue`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:window.location.href,fields:e,company:t,position:o,resumeMd:n,resumeId:i})});resumeValueJson=s.values}catch(e){throw isHttpResponseError=!0,httpResponseErrorAct="fillResumeValue",e}return null}function formatResumeValueJson(e){const t=[];for(const o of e){const e={name:o.name,fields:[]};for(const t of o.fields)if(t.name.includes(" - ")){const[o,n]=t.name.split(" - ");let i=e.fields.find((e=>e.name===o));i||(i={name:o,blanks:[]},e.fields.push(i)),i.blanks.push({name:n,value:t.value})}else e.fields.push({name:t.name,blanks:[{name:"",value:t.value}]});t.push(e)}return t}async function fillBlankDoms(e,t){try{let o=0;for(;o<e.length;o++){const n=e[o],i=[];for(const e of t)e.name==n.name&&i.push(e.fields);if(0==i.length)continue;const s=[n.fields];for(let t=1;t<i.length;t++){let t=null;o+1<e.length&&(t=e[o+1].dom);const i=_findAddDomBetweenDoms(n.dom,t);if(!i)break;_observeDomChanges(),await _clickDom(i),await waitTime(100),_stopObserveDomChanges();const l=_getNewBlockDoms();if(0==l.length)break;_blockRemoveDoms=[];const a=[];for(const e of l){const t=window.innerHeight,o=e.getBoundingClientRect(),n=window.scrollY+o.top-t/2;window.scrollTo({top:n,left:0,behavior:"instant"}),await waitTime(10);const i=_getCertainInputDoms(e.querySelectorAll("*"));let s=i.inputDoms,l=i.selectDoms;s=_getPossibleInputDoms(s,l,_getBorderHeightList(s),e.querySelectorAll("*")),s=_uniqueSamePositionInputDoms(s),a.push(...s)}for(const e of a)await _focusDom(e),await waitTime(20),await _blurDom(e),await waitTime(10);await waitTime(600);const r=_findFieldDomInNewBlockDoms(l,n.fields);if(!r)break;for(const e of r){e.field.dom&&(e.field.dom.style.backgroundColor="var(--highlight-yellow)");for(const t of e.blanks)t.dom.style.backgroundColor="var(--highlight-green)"}s.push(r)}for(let e=0;e<s.length&&!(e>=i.length);e++){const t=s[e],o=i[e];for(const e of t){let t=0;for(const n of e.blanks){let i="";for(const s of o)for(let o=t;o<s.blanks.length;o++){const l=s.blanks[o];if(s.name==e.name&&l.name==n.name){i=l.value,t=o+1;break}}if(""==i)continue;const s=`尝试为你填写 ${e.name.substring(0,8)}...`;window.setStateText(s),n.dom.scrollIntoView({block:"center"}),await waitTime(100);const l=n.dom.style.backgroundColor;if(n.dom.style.backgroundColor="var(--highlight-red)",await waitTime(200),"input"==n.type){_observeDomChanges(),await _clickDom(n.dom),await waitTime(500),/时间|日期|年月/.test(e.name)&&await waitTime(300),_checkNewModalDom()&&await waitTime(400),_stopObserveDomChanges();let t=!1;if(_checkNewModalDom()){const e=_checkDateModal();if(e)await _chooseDateModal(e,i),t=!0;else if(_checkSpecialDateModal())await _chooseSpecialDateModal(n.dom,i),t=!0;else{const e=_getObserveModalItemDom(i);e&&(await _clickDom(e),await waitTime(200),t=!0)}await _clickSubmitModal(),await _checkModalClosed(n.dom,i)||await _clickCloseModal(n.dom),_clearObserveDoms()}if(!t){let e=!1;if(("INPUT"===n.dom.tagName||"TEXTAREA"===n.dom.tagName||n.dom.isContentEditable)&&(e=!0),e){_observeDomChanges(),await _focusDom(n.dom),await waitTime(50),"INPUT"===n.dom.tagName||"TEXTAREA"===n.dom.tagName?n.dom.value=i:n.dom.isContentEditable&&(n.dom.textContent=i);const e=new Event("input",{bubbles:!0});n.dom.dispatchEvent(e);const t=new Event("change",{bubbles:!0});if(n.dom.dispatchEvent(t),await waitTime(300),_checkNewModalDom()&&await waitTime(1200),_stopObserveDomChanges(),_checkNewModalDom()){const e=_getObserveModalItemDom(i);e&&(await waitTime(200),await _clickDom(e),await waitTime(200)),await _clickSubmitModal(),await _checkModalClosed(n.dom,i)||await _clickCloseModal(n.dom),_clearObserveDoms()}}}for(n.dom.style.backgroundColor=l,await _blurDom(n.dom),await waitTime(200);!window.isRunning();)await waitTime(500)}else if("select"==n.type){const e=_getModalItemDomByValue(n.dom,i);if(e){const t=e.value;null!==t&&(n.dom.value=t),await waitTime(50)}for(n.dom.style.backgroundColor=l;!window.isRunning();)await waitTime(500)}else if("radio"==n.type){const e=_getModalItemDomByValue(n.dom,i);for(e&&(await _clickDom(e),await waitTime(50)),n.dom.style.backgroundColor=l;!window.isRunning();)await waitTime(500)}}}}}}catch(e){}finally{e.length>0&&(e[0].dom?e[0].dom.scrollIntoView({block:"center"}):e[0].fields.length>0&&e[0].fields[0].field.dom&&e[0].fields[0].field.dom.scrollIntoView({block:"center"}))}}function _checkDateModal(){const e=_filterDomsLeaveParent(_observeAddDoms);for(const t of e){const e=_getModalItems(t);if(e.length<12)continue;let o=e[0].replace(/\s+/g,""),n=e[1].replace(/\s+/g,""),i=e[2].replace(/\s+/g,"");if(/^(19|20)\d{2}(年)?$/.test(o)&&/^(0?[1-9]|1[0-2]|一)月$/.test(n))return/^(19|20)\d{2}(年)?\-(19|20)\d{2}(年)?$/.test(i),t;if(/^(19|20)\d{2}(年)?\-(19|20)\d{2}(年)?$/.test(o)&&/^((19|20)\d{2}|(0?[1-9]|1[0-2]|一)月)$/.test(n))return t}return null}function _checkSpecialDateModal(){const e=_filterDomsLeaveParent(_observeAddDoms);for(const t of e)if("_my97DP"===t.id)return t;return null}async function _chooseSpecialDateModal(e,t){let o,n,i="1";if("至今"===t)o=(new Date).getFullYear(),n=(new Date).getMonth()+1,i=(new Date).getDate();else{const e=t.match(/^(\d{4})(-|\.|年)(\d{1,2})(?:月)?(?:(-|\.|月)(\d{1,2})(日)?)?$/);if(!e)return;o=e[1],n=e[3],i=e[5]||"01"}const s=`${o}-${n}-${i}`;"INPUT"===e.tagName||"TEXTAREA"===e.tagName?e.value=s:e.isContentEditable&&(e.textContent=s);const l=new Event("input",{bubbles:!0});e.dispatchEvent(l);const a=new Event("change",{bubbles:!0});e.dispatchEvent(a),await waitTime(300)}async function _chooseDateModal(e,t){let o,n,i="1";if("至今"===t)o=(new Date).getFullYear(),n=(new Date).getMonth()+1,i=(new Date).getDate();else{const e=t.match(/^(\d{4})(-|\.|年)(\d{1,2})(?:月)?(?:(-|\.|月)(\d{1,2})(日)?)?$/);if(!e)return;o=e[1],n=e[3],i=e[5]||"1",n.startsWith("0")&&(n=n.slice(1)),i.startsWith("0")&&(i=i.slice(1))}try{let t=_getModalItems(e),s=_washDateItems(t),l=s.join(";");if(/(^|;)(19|20)\d{2}(年)?\-(19|20)\d{2}(年)?/.test(l)){let n=0;for(;n<10;n++){let n=s.indexOf(o+"年");if(-1===n&&(n=s.indexOf(o)),-1!==n){const o=_getModalItemDomByValue(e,t[n]);await _clickDom(o),await waitTime(50),t=_getModalItems(e),s=_washDateItems(t),l=s.join(";");break}{const n=_getModalItemDomByValue(e,t[0]),[i,a]=_findDateLeftAndRight(e,n);if(!i||!a)return;let r=null;for(let e=0;e<s.length-2;e++){const t=s[e].match(/^(19|20)\d{2}$/),o=s[e+1].match(/^(19|20)\d{2}$/),n=s[e+2].match(/^(19|20)\d{2}$/);if(t&&o&&n){const e=parseInt(t[0]),i=parseInt(o[0]),s=parseInt(n[0]);if(i===e+1&&s===i+1){r=e;break}}}if(null===r)return;const c=parseInt(o)<r?i:a;await _clickDom(c),await waitTime(50),t=_getModalItems(e),s=_washDateItems(t),l=s.join(";")}}if(10===n)return}else{if(/1;2;3;4;5;6;7;8;9;10;11;12;13;14;15;16;17;18;19;20;21;22;23;24;25;26;27;28/.test(l)){if(s[0].replace(/年$/,"")!==o){const n=_getModalItemDomByValue(e,t[0]);await _clickDom(n),await waitTime(50),t=_getModalItems(e),s=_washDateItems(t),l=s.join(";");let i=0;for(;i<10;i++){let n=s.indexOf(o+"年");if(-1===n&&(n=s.indexOf(o)),-1!==n){const o=_getModalItemDomByValue(e,t[n]);await _clickDom(o),await waitTime(50),t=_getModalItems(e),s=_washDateItems(t),l=s.join(";");break}{const n=_getModalItemDomByValue(e,t[0]),[i,a]=_findDateLeftAndRight(e,n);if(!i||!a)return;let r=null;for(let e=0;e<s.length-2;e++){const t=s[e].match(/^(19|20)\d{2}$/),o=s[e+1].match(/^(19|20)\d{2}$/),n=s[e+2].match(/^(19|20)\d{2}$/);if(t&&o&&n){const e=parseInt(t[0]),i=parseInt(o[0]),s=parseInt(n[0]);if(i===e+1&&s===i+1){r=e;break}}}if(null===r)return;const c=parseInt(o)<r?i:a;await _clickDom(c),await waitTime(50),t=_getModalItems(e),s=_washDateItems(t),l=s.join(";")}}if(10===i)return}let a=!1;if(/1;2;3;4;5;6;7;8;9;10;11;12;13;14;15;16;17;18;19;20;21;22;23;24;25;26;27;28/.test(l)){let o=!1;for(let n=0;n<s.length-1;n++)if("14"===s[n]&&"15"===s[n+1]){const i=_getModalItemDomByValue(e,t[n]),s=_getModalItemDomByValue(e,t[n+1]);_checkDomByViewport(i)&&_checkDomByViewport(s)&&(o=!0);break}if(o){if(s[1]!==n+"月"){const o=_getModalItemDomByValue(e,t[1]);await _clickDom(o),await waitTime(50),t=_getModalItems(e),s=_washDateItems(t),l=s.join(";"),a=!0}}else a=!0}else a=!0;if(a){const o=_getModalItemDomByValue(e,t[s.indexOf(n+"月")]);await _clickDom(o),await waitTime(50),t=_getModalItems(e),s=_washDateItems(t),l=s.join(";")}const r=_getModalItemDomByValue(e,i,Number(i)<15);return await _clickDom(r),void await waitTime(50)}if(/1月;2月;3月;4月;5月;6月;7月;8月;9月;10月;11月;12月/.test(l)){if(s[0].replace(/年$/,"")!==o){let n=t[0];s.length>3&&/^(19|20)\d{2}$/.test(s[2])&&(n=t[2]);const i=_getModalItemDomByValue(e,n);await _clickDom(i),await waitTime(50),t=_getModalItems(e),s=_washDateItems(t),l=s.join(";");let a=0;for(;a<10;a++){let n=s.indexOf(o+"年");if(-1===n&&(n=s.indexOf(o)),-1!==n){const o=_getModalItemDomByValue(e,t[n]);if(await _clickDom(o),await waitTime(50),t=_getModalItems(e),s=_washDateItems(t),l=s.join(";"),/^(19|20)\d{2}(年)?\-(19|20)\d{2}(年)?/.test(l)&&!/1月;2月;3月;4月;5月;6月;7月;8月;9月;10月;11月;12月/.test(l))continue;break}{const n=_getModalItemDomByValue(e,t[0]),[i,a]=_findDateLeftAndRight(e,n);if(!i||!a)return;let r=null;for(let e=0;e<s.length-2;e++){const t=s[e].match(/^(19|20)\d{2}$/),o=s[e+1].match(/^(19|20)\d{2}$/),n=s[e+2].match(/^(19|20)\d{2}$/);if(t&&o&&n){const e=parseInt(t[0]),i=parseInt(o[0]),s=parseInt(n[0]);if(i===e+1&&s===i+1){r=e;break}}}if(null===r)return;const c=parseInt(o)<r?i:a;await _clickDom(c),await waitTime(50),t=_getModalItems(e),s=_washDateItems(t),l=s.join(";")}}if(10===a)return}let i=s.lastIndexOf(n+"月");if(-1===i)return;const a=_getModalItemDomByValue(e,t[i],!1);return await _clickDom(a),void await waitTime(50)}}}catch(e){}}function _washDateItems(e){const t=[];for(const o of e){let e=o.replace(/\s+/g,"");e=e.replace(/十一月/,"11月").replace(/十二月/,"12月").replace(/一月/,"1月").replace(/二月/,"2月").replace(/三月/,"3月").replace(/四月/,"4月").replace(/五月/,"5月").replace(/六月/,"6月").replace(/七月/,"7月").replace(/八月/,"8月").replace(/九月/,"9月").replace(/十月/,"10月"),t.push(e)}return t}function _checkDomByViewport(e){const t=e.getBoundingClientRect();if(t.top<0||t.left<0||t.bottom>window.innerHeight||t.right>window.innerWidth)return!1;const o=document.elementFromPoint(t.left+t.width/2,t.top+t.height/2);return!!o&&(o===e||e.contains(o)||o.contains(e))}function _findDateLeftAndRight(e,t){const o=t.getBoundingClientRect(),n=e.querySelectorAll("*");let i=[],s=[];for(const e of n){if(e instanceof SVGElement||e.closest("svg"))continue;const t=e.getBoundingClientRect();t.width>50||t.height>50||(t.top<o.top-20||t.bottom>o.bottom+20||(t.left<o.left&&t.right<o.left&&i.push(e),t.left>o.right&&t.right>o.right&&s.push(e)))}if(i=_filterDomsLeaveChildren(i),s=_filterDomsLeaveChildren(s),0===i.length)return[null,null];if(0===s.length)return[null,null];return[i[0],s[s.length-1]]}function _getObserveModalItemDom(e){const t=_filterDomsLeaveParent(_observeAddDoms);let o=null;for(const n of t)o||(o=_getModalItemDomByValue(n,e)),_getModalSubmitDoms(n),_getModalCancelDoms(n);return o}function _getModalItemDomByValue(e,t,o=!0){const n=e.querySelectorAll("*");let i=null,s=null;for(const e of n){if(0==e.childNodes.length)continue;let n=!0;for(const t of e.childNodes)if(t.nodeType!==Node.TEXT_NODE){n=!1;break}if(n){if(e.textContent.trim()===t&&_isDomVisible(e)){if(i=e,o)return e}else e.textContent.startsWith(t)&&(s&&o||(s=e))}}return!i&&s?s:i}async function _checkModalClosed(e,t){for(let o=0;o<10;o++){if(await waitTime(100),e.value===t||e.textContent===t)return!0;{let e=!0;for(const t of _observeAddDoms)if(_isDomVisible(t)){e=!1;break}if(e)return!0}}return!1}function _findAddDomBetweenDoms(e,t){const o=_documentQuerySelectorAll().reverse();let n=!t;for(let i=0;i<o.length;i++){const s=o[i];if(s===t||s.contains(t)){n=!0;continue}if(!n)continue;if(s===e)return null;const l=new RegExp("^[\\s\\*\\+]*(添加|增加|新增).{0,10}$");if(l.test(s.textContent))return _findMinDomInDom(s,l)}return null}function _findFieldDomInNewBlockDoms(e,t){const o=[];for(const t of e)o.push(t),o.push(...t.querySelectorAll("*"));const n=JSON.parse(JSON.stringify(t,((e,t)=>t instanceof HTMLElement?null:t)));for(let e=0;e<t.length;e++){const i=t[e],s=n[e];let l=null;if(i.field.dom){if(l=_findSimilarDom(o,i.field.dom),!l)return null;s.field.dom=l,o.splice(o.indexOf(l),1)}for(let e=0;e<i.blanks.length;e++){const t=i.blanks[e],n=s.blanks[e];if(l=_findSimilarDom(o,t.dom),!l)return null;n.dom=l,o.splice(o.indexOf(l),1)}}return n}function _findSimilarDom(e,t){for(const o of e){if(o.className!==t.className)continue;if(o.textContent.trim()!==t.textContent.trim())continue;let e=t.parentElement,n=o.parentElement,i=!0;for(;e&&n&&e!==n;){if(e.tagName!==n.tagName||e.className!==n.className){if(e.tagName===n.tagName&&"string"==typeof e.className&&"string"==typeof n.className&&e.className.length===n.className.length){const t=/[^a-zA-Z0-9]/g;let o=[...e.className.matchAll(t)],i=[...n.className.matchAll(t)];const s=o.length===i.length,l=o.every(((e,t)=>{const o=i[t];return e.index===o.index&&e[0]===o[0]}));if(s&&l){e=e.parentElement,n=n.parentElement;continue}}if(e.tagName===n.tagName&&"string"==typeof e.className&&"string"==typeof n.className){const t=e.className.trim().split(/\s+/),o=n.className.trim().split(/\s+/),i=t.filter((e=>!o.includes(e))),s=o.filter((e=>!t.includes(e)));let l=!0;for(const e of[...i,...s])if(!/focus|active|selected|hover|error|alert|red|warning/.test(e)){l=!1;break}if(l){e=e.parentElement,n=n.parentElement;continue}}i=!1;break}e=e.parentElement,n=n.parentElement}if(i)return o}return null}async function _clickDom(e){let t=e;if(e.scrollIntoViewIfNeeded(),e.offsetWidth>0&&e.offsetHeight>0&&"hidden"!==window.getComputedStyle(e).visibility){const o=e.getBoundingClientRect(),n=o.left+o.width/2,i=o.top+o.height/2;t=document.elementFromPoint(n,i),!t||e.contains(t)||t.contains(e)||e.parentNode==t.parentNode||(t=e)}if(t)for(;t&&"function"!=typeof t.click;)t=t.parentElement;else t=e;let o=null;const n={bubbles:!0,cancelable:!0,view:window};o=new MouseEvent("mousedown",n),t.dispatchEvent(o),o=new FocusEvent("focus",n),t.dispatchEvent(o),o=new MouseEvent("mouseup",n),t.dispatchEvent(o),o=new MouseEvent("click",n),t.dispatchEvent(o),await waitTime(10)}async function _focusDom(e){const t=new FocusEvent("focus",{bubbles:!0,cancelable:!0,view:window});e.dispatchEvent(t),await waitTime(10)}async function _blurDom(e){const t=new FocusEvent("blur",{bubbles:!0,cancelable:!0,view:window});e.dispatchEvent(t),await waitTime(10)}async function _keydownDom(e,t){const o={ArrowDown:40,ArrowUp:38,ArrowLeft:37,ArrowRight:39,Enter:13,Escape:27,Tab:9,Space:32,Backspace:8,Delete:46,Home:36,End:35,PageUp:33,PageDown:34}[t]||0;if(0===o)return;const n=new KeyboardEvent("keydown",{bubbles:!0,cancelable:!0,key:t,keyCode:o,code:t,which:o});e.dispatchEvent(n),await waitTime(10)}function _documentQuerySelectorAll(){let e=document.body.querySelectorAll("*:not(#ark-ai)");return Array.from(e)}function _isDomVisible(e){if(!e.ownerDocument.contains(e))return!1;let t=e;for(;t;){const e=getComputedStyle(t);if("none"===e.display||"hidden"===e.visibility||"0"===e.opacity||t.hidden||0===t.offsetWidth&&0===t.offsetHeight&&"hidden"===e.overflow)return!1;t=t.parentElement}if(function e(t){const o=getComputedStyle(t);if("none"===o.display||"hidden"===o.visibility||"0"===o.opacity||t.hidden||0===t.offsetWidth&&0===t.offsetHeight&&"hidden"===o.overflow)return!0;if(0===t.offsetHeight||0===t.offsetWidth){for(const o of t.children)if(!e(o))return!1;return!0}return!1}(e))return!1;return!!function(e){const t=e.getBoundingClientRect();let o=e.parentElement;for(;o;){const e=getComputedStyle(o);if("hidden"===e.overflow||"hidden"===e.overflowY||"hidden"===e.overflowX){const e=o.getBoundingClientRect(),n=t.top,i=t.bottom,s=e.top,l=e.bottom;if(i<=s||n>=l)return!1;if((Math.min(i,l)-Math.max(n,s))/t.height<.1)return!1}o=o.parentElement}return!0}(e)}function _isRedColor(e){let t,o,n;if(e.startsWith("rgb")){const i=e.match(/\d+/g);if(!i||i.length<3)return!1;[t,o,n]=i.map(Number)}else{if(!e.startsWith("#"))return!1;t=parseInt(e.slice(1,3),16),o=parseInt(e.slice(3,5),16),n=parseInt(e.slice(5,7),16)}return t>1.5*o&&t>1.5*n}function _isRadioDom(e){return/(?:^|[^a-zA-Z])radio(?:[^a-zA-Z]|$)/.test(e.className)&&_getModalItems(e).length>=2}function _filterDomsLeaveParent(e){const t=[];for(const o of e){let n=!0;for(const t of e)if(t!==o&&t.contains(o)){n=!1;break}n&&t.push(o)}return t}function _filterDomsLeaveChildren(e){const t=[];for(const o of e){let n=!0;for(const t of e)if(t!==o&&o.contains(t)){n=!1;break}n&&t.push(o)}return t}function _findMinDomInDom(e,t){let o=e;const n=Array.from(e.querySelectorAll("*")).reverse();for(const e of n)if(t.test(e.textContent)){o=e;break}return o}let _observeAddDoms=[],_oldComputedStyles=new WeakMap;function _observeDomChanges(){const e=document.body,t=new WeakSet,o=Array.from(e.querySelectorAll("*"));o.push(e);for(const e of o)if(e.nodeType===Node.ELEMENT_NODE){const t=getComputedStyle(e);_oldComputedStyles.set(e,{display:t.display,visibility:t.visibility,opacity:t.opacity})}_domObserver=new MutationObserver((e=>{for(const o of e)if("childList"===o.type){for(const e of o.addedNodes)if(e.nodeType===Node.ELEMENT_NODE&&!t.has(e)){t.add(e),_observeAddDoms.push(e);for(const o of e.querySelectorAll("*"))t.add(o)}}else if("attributes"===o.type){const e=o.target;if(e.nodeType!==Node.ELEMENT_NODE||t.has(e))continue;const n=_oldComputedStyles.get(e)||{},i=getComputedStyle(e),s="none"===n.display||"hidden"===n.visibility||0===parseFloat(n.opacity),l="none"!==i.display&&"hidden"!==i.visibility&&0!==parseFloat(i.opacity);if(_oldComputedStyles.set(e,{display:i.display,visibility:i.visibility,opacity:i.opacity}),s&&l){t.add(e),_observeAddDoms.push(e);for(const o of e.querySelectorAll("*"))t.add(o)}}})),_domObserver.observe(e,{childList:!0,subtree:!0,attributeFilter:["class","style"]})}function _stopObserveDomChanges(){_domObserver&&(_domObserver.disconnect(),_domObserver=null,_oldComputedStyles=new WeakMap)}function _getSelectItems(e){const t=[],o=e.querySelectorAll("*");for(const e of o){let o=!0;for(const t of e.childNodes)if(t.nodeType!==Node.TEXT_NODE){o=!1;break}if(o&&e.childNodes.length>0){const o=e.textContent.trim();""!==o&&t.push(o)}}return t}function _getModalItems(e){const t=[],o=e.querySelectorAll("*");for(const e of o){if(!_isDomVisible(e))continue;let o=!0;for(const t of e.childNodes)if(t.nodeType!==Node.TEXT_NODE){o=!1;break}if(o&&e.childNodes.length>0){const o=e.textContent.trim();""!==o&&t.push(o)}}return t}let _modalSubmitDoms=[];function _getModalSubmitDoms(e){const t=e.querySelectorAll("*");for(const e of t)if("确定"===e.innerHTML.replace(/\s+/g,"")&&_isDomVisible(e)){_modalSubmitDoms.push(e);break}}let _modalCancelDoms=[];function _getModalCancelDoms(e){const t=e.querySelectorAll("*");for(const e of t)if("取消"===e.innerHTML.replace(/\s+/g,"")&&_isDomVisible(e)){_modalCancelDoms.push(e);break}for(const e of t){const t=e.getBoundingClientRect();if(t.width===window.innerWidth&&t.height===window.innerHeight)_isDomVisible(e)&&_modalCancelDoms.push(e);else if(_modalCancelDoms.length>0)break}}let _modalDeleteDoms=[];function _getModalDeleteDoms(e){const t=e.querySelectorAll("*");for(const e of t)if(["删除","确定删除"].includes(e.innerHTML.replace(/\s+/g,""))&&_isDomVisible(e)){_modalDeleteDoms.push(e);break}}async function _clickSubmitModal(){if(_modalSubmitDoms.length>0)for(const e of _modalSubmitDoms)_isDomVisible(e)&&await _clickDom(e)}async function _clickCloseModal(e){if(_modalCancelDoms.length>0)for(const e of _modalCancelDoms)_isDomVisible(e)&&await _clickDom(e);else await _clickDom(e);await waitTime(10),await _blurDom(e);let t=!0;for(const e of _observeAddDoms)if(_isDomVisible(e)){t=!1;break}if(!t){const e=new Event("mousedown",{bubbles:!0});document.body.dispatchEvent(e)}}async function _clickDeleteModal(){if(_modalDeleteDoms.length>0)for(const e of _modalDeleteDoms)_isDomVisible(e)&&await _clickDom(e)}function _clearObserveDoms(){_observeAddDoms=[],_modalCancelDoms=[],_modalSubmitDoms=[],_modalDeleteDoms=[]}let isHttpResponseError=!1,httpResponseErrorAct="";function _checkResponseError(){if(isHttpResponseError)throw new Error("网络响应不正常")}async function addStatistics(){const e=await chrome.storage.local.get(["websiteCount","fieldCount"]),t=Number(e.websiteCount)||0,o=Number(e.fieldCount)||0;let n=0;for(const e of resumeValueJson)for(const t of e.fields)t.value&&n++;await chrome.storage.local.set({websiteCount:t+1,fieldCount:o+n})}let learningInterval=null,learningHtml="",currentURL="";async function startLearningField(){currentURL=window.location.href,await _isNeedLearning()&&(learningInterval||(learningInterval=setInterval((()=>{window.location.href!==currentURL?(clearInterval(learningInterval),learningInterval=null,currentURL=window.location.href):_sendHtmlToBackground()}),5e3)))}async function _isNeedLearning(){return!1!==(await chrome.storage.local.get(["learningResume"])).learningResume}function _sendHtmlToBackground(){const e=cleanHtml();e!==learningHtml&&(""!==learningHtml?(learningHtml=e,chrome.runtime.sendMessage({type:"learnField",url:window.location.href,html:e},(e=>{})),window.setStateText("正在智能学习你新填写的内容~"),window.changeStartButtonState("learning")):learningHtml=e)}function _stopLearningToBackground(){const e=cleanHtml();chrome.runtime.sendMessage({type:"stopLearnField",url:window.location.href,html:e},(e=>{}))}async function addHistory(e,t){if(window.campusSource){_sendHistoryToBackground("campus",{campusId:window.campusSource.campusId})}else{_sendHistoryToBackground("user",{url:await _getHistoryUrl(),company:e||"未知公司",position:t||"未知职位"})}}function _sendHistoryToBackground(e,t){chrome.runtime.sendMessage({type:"addHistory",source:e,data:t},(e=>{}))}async function _getHistoryUrl(){try{const e=window.location.href,t=(await chrome.runtime.sendMessage({type:"getHistoryUrls"})).filter((t=>t!==e)),o=["position"],n=["login","signin","register","resume"],i=t.find((e=>{const t=e.toLowerCase();return o.some((e=>t.includes(e)))&&!n.some((e=>t.includes(e)))}));if(i)return i;const s=t.find((e=>{const t=e.toLowerCase();return!n.some((e=>t.includes(e)))}));return s||e}catch(e){return window.location.href}}function logErrorToServer(e,t,o,n,i){const s=new Date,l=Math.floor((s-o)/1e3);let a="Unknown";-1!==navigator.userAgent.indexOf("Edg")?a="Edge":-1!==navigator.userAgent.indexOf("Chrome")&&(a="Chrome");let r="Unknown";try{r=chrome.runtime.getManifest().version}catch(e){}chrome.runtime.sendMessage({type:"logError",functionName:e,errorStack:t,duration:l,browser:a,version:r,resumeId:i,company:n||""},(e=>{}))}async function initConfig(){const e=await chrome.storage.local.get(["config"]);window.config=e.config}async function fetchWithJwt(e,t={}){try{const o=await chrome.runtime.sendMessage({type:"fetchWithJwt",url:e,options:t});if(o.error)throw new Error(o.error);return o}catch(e){throw isHttpResponseError=!0,httpResponseErrorAct="fetchWithJwt",e}}function waitTime(e=1e3){return new Promise((t=>{setTimeout((()=>{t()}),e)}))}(async()=>{await initConfig()})();