"""
网页自动化模块 - 处理浏览器操作和表单填写
"""

import os
import time
import logging
from typing import List, Dict, Any, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import json
from enhanced_dropdown_handler import EnhancedDropdownHandler

logger = logging.getLogger(__name__)


class WebAutomation:
    """网页自动化类"""
    
    def __init__(self, browser: str = 'chrome', headless: bool = False):
        self.browser_type = browser
        self.headless = headless
        self.driver = None
        self.wait = None
        self.dropdown_handler = None
        
    def init_browser(self):
        """初始化浏览器"""
        try:
            if self.browser_type == 'chrome':
                options = webdriver.ChromeOptions()
                if self.headless:
                    options.add_argument('--headless')
                options.add_argument('--disable-blink-features=AutomationControlled')
                options.add_experimental_option("excludeSwitches", ["enable-automation"])
                options.add_experimental_option('useAutomationExtension', False)
                self.driver = webdriver.Chrome(options=options)
            elif self.browser_type == 'edge':
                options = webdriver.EdgeOptions()
                if self.headless:
                    options.add_argument('--headless')
                self.driver = webdriver.Edge(options=options)
            elif self.browser_type == 'firefox':
                options = webdriver.FirefoxOptions()
                if self.headless:
                    options.add_argument('--headless')
                self.driver = webdriver.Firefox(options=options)
            else:
                raise ValueError(f"不支持的浏览器类型: {self.browser_type}")
                
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, 10)
            self.dropdown_handler = EnhancedDropdownHandler(self.driver)
            logger.info(f"成功初始化{self.browser_type}浏览器")
            
        except Exception as e:
            logger.error(f"浏览器初始化失败: {e}")
            raise
            
    def take_screenshot(self, filename: str) -> str:
        """截取网页截图"""
        try:
            os.makedirs('screenshots', exist_ok=True)
            filepath = os.path.join('screenshots', filename)
            self.driver.save_screenshot(filepath)
            logger.info(f"截图保存至: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"截图失败: {e}")
            raise
            
    def take_full_page_screenshot(self, filename: str) -> str:
        """截取整个网页的完整截图"""
        try:
            os.makedirs('screenshots', exist_ok=True)
            filepath = os.path.join('screenshots', filename)
            
            # 提示用户调整浏览器窗口
            print("\n" + "="*50)
            print("📸 准备截图")
            print("="*50)
            print("请手动调整浏览器窗口大小，确保:")
            print("1. 所有需要填写的表单字段都可见")
            print("2. 缩放比例合适，字段清晰可辨")
            print("3. 页面内容完整显示在屏幕内")
            print("4. 调整完成后按回车键继续...")
            print("="*50)
            input()
            
            # 直接截图当前显示的内容
            self.driver.save_screenshot(filepath)
            
            logger.info(f"完整页面截图保存至: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"截图失败: {e}")
            # 如果截图失败，回退到标准截图
            return self.take_screenshot(filename)
            
    def wait_for_user_ready(self):
        """等待用户准备就绪"""
        print("\n" + "="*50)
        print("请完成以下步骤：")
        print("1. 登录招聘网站")
        print("2. 进入简历填写页面")
        print("3. 准备就绪后按回车键继续...")
        print("="*50)
        input()
        print("开始自动填写...")
        
    def get_form_elements(self) -> List[Dict[str, Any]]:
        """获取页面中的表单元素"""
        elements = []
        
        # 查找所有输入框
        inputs = self.driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            elem_info = self._extract_element_info(inp, "input")
            if elem_info:
                elements.append(elem_info)
                
        # 查找所有文本域
        textareas = self.driver.find_elements(By.TAG_NAME, "textarea")
        for textarea in textareas:
            elem_info = self._extract_element_info(textarea, "textarea")
            if elem_info:
                elements.append(elem_info)
                
        # 查找所有下拉菜单
        selects = self.driver.find_elements(By.TAG_NAME, "select")
        for select in selects:
            elem_info = self._extract_element_info(select, "select")
            if elem_info:
                elements.append(elem_info)
                
        # 查找可能的自定义下拉菜单
        custom_selects = self._find_custom_dropdowns()
        elements.extend(custom_selects)
                
        return elements
        
    def _find_custom_dropdowns(self) -> List[Dict[str, Any]]:
        """查找自定义下拉菜单"""
        custom_dropdowns = []
        
        # 常见的自定义下拉菜单选择器
        selectors = [
            'div[role="combobox"]',
            'div[role="listbox"]',
            '.ant-select',
            '.el-select',
            '.MuiSelect-root',
            '.dropdown',
            'input[readonly]'
        ]
        
        for selector in selectors:
            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
            for elem in elements:
                if elem.is_displayed():
                    elem_info = self._extract_element_info(elem, "custom_select")
                    if elem_info:
                        custom_dropdowns.append(elem_info)
                        
        return custom_dropdowns
        
    def _extract_element_info(self, element, elem_type: str) -> Optional[Dict[str, Any]]:
        """提取元素信息"""
        try:
            # 跳过隐藏元素
            if not element.is_displayed():
                return None
                
            info = {
                'type': elem_type,
                'element': element,
                'id': element.get_attribute('id'),
                'name': element.get_attribute('name'),
                'placeholder': element.get_attribute('placeholder'),
                'value': element.get_attribute('value'),
                'required': element.get_attribute('required') is not None,
                'class': element.get_attribute('class'),
                'role': element.get_attribute('role'),
                'aria-label': element.get_attribute('aria-label'),
            }
            
            # 获取标签文本
            label_text = self._find_label_for_element(element)
            info['label'] = label_text
            
            # 对于标准select下拉菜单，获取选项
            if elem_type == 'select':
                options = element.find_elements(By.TAG_NAME, 'option')
                info['options'] = [opt.text for opt in options if opt.text]
                
            # 对于输入框，获取类型
            if elem_type == 'input':
                info['input_type'] = element.get_attribute('type') or 'text'
                
            return info
            
        except Exception as e:
            logger.debug(f"提取元素信息失败: {e}")
            return None
            
    def _find_label_for_element(self, element) -> str:
        """查找元素对应的标签文本"""
        try:
            # 通过for属性查找
            elem_id = element.get_attribute('id')
            if elem_id:
                labels = self.driver.find_elements(By.CSS_SELECTOR, f'label[for="{elem_id}"]')
                if labels:
                    return labels[0].text
                    
            # 查找父元素中的标签
            parent = element.find_element(By.XPATH, '..')
            labels = parent.find_elements(By.TAG_NAME, 'label')
            if labels:
                return labels[0].text
                
            # 查找aria-labelledby指向的元素
            labelledby = element.get_attribute('aria-labelledby')
            if labelledby:
                label_elem = self.driver.find_element(By.ID, labelledby)
                return label_elem.text
                
            # 查找附近的文本节点
            # 这里可以添加更复杂的逻辑
            
        except:
            pass
            
        return ""
        
    def fill_text_field(self, element, value: str):
        """填写文本字段"""
        try:
            # 清空原有内容
            element.clear()
            # 填入新内容
            element.send_keys(value)
            logger.info(f"成功填写文本字段: {value}")
        except Exception as e:
            logger.error(f"填写文本字段失败: {e}")
            
    def select_dropdown(self, element, value: str = None, index: int = None):
        """选择下拉菜单"""
        try:
            # 首先尝试标准select元素
            if element.tag_name == 'select':
                select = Select(element)
                
                if value:
                    # 优先按值选择
                    select.select_by_visible_text(value)
                elif index is not None:
                    # 按索引选择
                    select.select_by_index(index)
                else:
                    # 默认选择第一个非空选项
                    options = select.options
                    for i, opt in enumerate(options):
                        if opt.text.strip():
                            select.select_by_index(i)
                            break
                            
                logger.info(f"成功选择下拉菜单选项")
            else:
                # 使用增强的下拉菜单处理器处理自定义下拉菜单
                success = self.dropdown_handler.handle_dropdown(element, value, index)
                if success:
                    logger.info("成功处理自定义下拉菜单")
                else:
                    logger.warning("自定义下拉菜单处理失败")
                    
        except Exception as e:
            logger.error(f"选择下拉菜单失败: {e}")
            
    def click_element(self, element):
        """点击元素"""
        try:
            # 滚动到元素可见
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)
            
            # 尝试直接点击
            try:
                element.click()
            except:
                # 使用JavaScript点击
                self.driver.execute_script("arguments[0].click();", element)
                
            logger.info("成功点击元素")
        except Exception as e:
            logger.error(f"点击元素失败: {e}")
            
    def handle_date_picker(self, element, date_str: str):
        """处理日期选择器"""
        try:
            # 尝试直接输入
            element.clear()
            element.send_keys(date_str)
            
            # 有些日期选择器需要特殊处理
            # 这里可以根据具体情况添加更多逻辑
            
        except Exception as e:
            logger.error(f"处理日期选择器失败: {e}")
            
    def scroll_to_bottom(self):
        """滚动到页面底部"""
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        
    def scroll_to_element(self, element):
        """滚动到指定元素"""
        self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
        time.sleep(0.5)
        
    def get_page_html(self) -> str:
        """获取页面HTML"""
        return self.driver.page_source
        
    def extract_dynamic_content(self, element) -> Dict[str, Any]:
        """提取动态内容（如点击后才出现的下拉菜单）"""
        # 使用增强的下拉菜单处理器提取选项
        options = self.dropdown_handler.extract_all_options(element)
        return {'options': options} if options else {}
        
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭") 