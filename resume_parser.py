"""
简历解析器 - 解析markdown格式的简历文件
"""

import re
import markdown
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)


class ResumeParser:
    """简历解析器类"""
    
    def __init__(self, resume_path: str):
        self.resume_path = resume_path
        self.raw_content = ""
        self.parsed_data = {}
        
    def load_resume(self) -> str:
        """加载简历文件"""
        try:
            with open(self.resume_path, 'r', encoding='utf-8') as f:
                self.raw_content = f.read()
            logger.info(f"成功加载简历文件: {self.resume_path}")
            return self.raw_content
        except Exception as e:
            logger.error(f"加载简历文件失败: {e}")
            raise
            
    def parse(self) -> Dict[str, Any]:
        """解析简历内容"""
        if not self.raw_content:
            self.load_resume()
            
        lines = self.raw_content.split('\n')
        current_section = None
        sections = {}
        section_content = []
        
        for line in lines:
            line = line.strip()
            
            # 检测一级标题（主要部分）
            if line.startswith('# '):
                if current_section and section_content:
                    sections[current_section] = '\n'.join(section_content)
                current_section = line[2:].strip()
                section_content = []
            else:
                if current_section:
                    section_content.append(line)
                    
        # 保存最后一个部分
        if current_section and section_content:
            sections[current_section] = '\n'.join(section_content)
            
        # 解析特定字段
        self.parsed_data = {
            'basic_info': self._parse_basic_info(sections),
            'education': self._parse_education(sections),
            'experience': self._parse_experience(sections),
            'skills': self._parse_skills(sections),
            'awards': self._parse_awards(sections),
            'raw_sections': sections
        }
        
        return self.parsed_data
        
    def _parse_basic_info(self, sections: Dict) -> Dict:
        """解析基本信息"""
        info = {}
        
        # 姓名通常是第一个标题
        for key in sections:
            if '刘青昀' in key or any(name in key for name in ['姓名', '名字']):
                info['name'] = '刘青昀'
                basic_text = sections.get(key, '')
                
                # 提取电话
                phone_match = re.search(r'电话[：:]\s*([\d-]+)', basic_text)
                if phone_match:
                    info['phone'] = phone_match.group(1)
                    
                # 提取邮箱
                email_match = re.search(r'邮箱[：:]\s*(\S+@\S+)', basic_text)
                if email_match:
                    info['email'] = email_match.group(1)
                break
                
        return info
        
    def _parse_education(self, sections: Dict) -> List[Dict]:
        """解析教育经历"""
        education_list = []
        
        for key, content in sections.items():
            if '教育' in key:
                # 解析教育经历内容
                lines = content.split('\n')
                current_edu = None
                
                for line in lines:
                    # 检测学校名称（通常包含"大学"）
                    if '大学' in line and '#' in line:
                        if current_edu:
                            education_list.append(current_edu)
                        current_edu = {
                            'school': line.replace('#', '').strip(),
                            'details': []
                        }
                    elif current_edu:
                        if line.strip():
                            # 检测时间
                            time_match = re.search(r'(\d{4}年\d{1,2}月)\s*(\d{4}年\d{1,2}月)?', line)
                            if time_match:
                                current_edu['start_date'] = time_match.group(1)
                                if time_match.group(2):
                                    current_edu['end_date'] = time_match.group(2)
                            # 检测GPA
                            elif 'GPA' in line:
                                gpa_match = re.search(r'GPA[：:]\s*([\d.]+/[\d.]+)', line)
                                if gpa_match:
                                    current_edu['gpa'] = gpa_match.group(1)
                            # 检测专业
                            elif any(keyword in line for keyword in ['专业', '金融工程', '经济学', '硕士', '学士']):
                                current_edu['major'] = line.strip()
                            else:
                                current_edu['details'].append(line.strip())
                                
                if current_edu:
                    education_list.append(current_edu)
                    
        return education_list
        
    def _parse_experience(self, sections: Dict) -> Dict:
        """解析工作/研究经历"""
        experience = {
            'research': [],
            'internship': []
        }
        
        # 解析研究经历
        for key, content in sections.items():
            if '研究经历' in key:
                experience['research'] = self._parse_experience_section(content)
                
        # 解析实习经历
        for key, content in sections.items():
            if '实习经历' in key:
                experience['internship'] = self._parse_experience_section(content)
                
        return experience
        
    def _parse_experience_section(self, content: str) -> List[Dict]:
        """解析经历部分"""
        experiences = []
        lines = content.split('\n')
        current_exp = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 检测新的经历项（通常以公司名或项目名开始）
            if '#' in line or ('公司' in line or '研究' in line or '项目' in line):
                if current_exp:
                    experiences.append(current_exp)
                current_exp = {
                    'title': line.replace('#', '').strip(),
                    'details': [],
                    'responsibilities': []
                }
            elif current_exp:
                # 检测时间
                time_match = re.search(r'(\d{4}年\d{1,2}月)\s*(\d{4}年\d{1,2}月)?', line)
                if time_match:
                    current_exp['start_date'] = time_match.group(1)
                    if time_match.group(2):
                        current_exp['end_date'] = time_match.group(2)
                # 检测职位
                elif any(keyword in line for keyword in ['实习生', '负责人', '助理']):
                    current_exp['position'] = line.strip()
                # 检测地点
                elif any(city in line for city in ['北京', '上海', '深圳', '广州']):
                    current_exp['location'] = line.strip()
                else:
                    current_exp['responsibilities'].append(line)
                    
        if current_exp:
            experiences.append(current_exp)
            
        return experiences
        
    def _parse_skills(self, sections: Dict) -> Dict:
        """解析技能"""
        skills = {
            'languages': [],
            'programming': [],
            'tools': [],
            'certificates': []
        }
        
        for key, content in sections.items():
            if '技能' in key or '个人技能' in key:
                lines = content.split('\n')
                for line in lines:
                    if '英语' in line:
                        skills['languages'].append(line.strip())
                    elif any(lang in line for lang in ['Python', 'SQL', 'Java', 'C++']):
                        skills['programming'].append(line.strip())
                    elif any(tool in line for tool in ['Office', 'Excel', 'Stata', 'Latex']):
                        skills['tools'].append(line.strip())
                        
        return skills
        
    def _parse_awards(self, sections: Dict) -> List[str]:
        """解析奖项"""
        awards = []
        
        for key, content in sections.items():
            if '奖' in key or '荣誉' in key:
                lines = content.split('\n')
                for line in lines:
                    if line.strip():
                        awards.append(line.strip())
                        
        return awards
        
    def get_field_value(self, field_name: str) -> Any:
        """根据字段名获取对应的值"""
        field_mapping = {
            '姓名': lambda: self.parsed_data['basic_info'].get('name', ''),
            '电话': lambda: self.parsed_data['basic_info'].get('phone', ''),
            '邮箱': lambda: self.parsed_data['basic_info'].get('email', ''),
            '学校': lambda: self.parsed_data['education'][0]['school'] if self.parsed_data['education'] else '',
            '专业': lambda: self.parsed_data['education'][0].get('major', '') if self.parsed_data['education'] else '',
            'GPA': lambda: self.parsed_data['education'][0].get('gpa', '') if self.parsed_data['education'] else '',
        }
        
        # 尝试模糊匹配
        for key, func in field_mapping.items():
            if key in field_name:
                return func()
                
        return ""
        
    def get_summary(self) -> str:
        """获取简历摘要"""
        if not self.parsed_data:
            self.parse()
            
        summary = []
        
        # 基本信息
        basic = self.parsed_data.get('basic_info', {})
        if basic:
            summary.append(f"姓名: {basic.get('name', 'N/A')}")
            summary.append(f"电话: {basic.get('phone', 'N/A')}")
            summary.append(f"邮箱: {basic.get('email', 'N/A')}")
            
        # 教育经历
        education = self.parsed_data.get('education', [])
        if education:
            summary.append("\n教育经历:")
            for edu in education:
                summary.append(f"- {edu.get('school', 'N/A')}, {edu.get('major', 'N/A')}")
                
        return '\n'.join(summary) 