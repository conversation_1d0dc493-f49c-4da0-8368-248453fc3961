#!/usr/bin/env python3
"""
基于arkContent.js的下拉菜单处理逻辑 - JavaScript方式实现
学习arkContent.js的核心思路，使用JavaScript注入的方式处理下拉菜单
"""

import logging
import time
import json
from typing import List, Dict, Any
from selenium.webdriver.common.by import By

logger = logging.getLogger(__name__)

class JavaScriptDropdownHandler:
    """使用JavaScript注入的方式处理下拉菜单，学习arkContent.js的思路"""

    def __init__(self, driver):
        self.driver = driver
        self.input_doms = []
        self.input_items = []
        self.select_doms = []
        self.select_items = []
        self.radio_doms = []
        self.radio_items = []

    def explore_all_dropdowns(self):
        """探索所有下拉菜单选项 - 学习arkContent.js的getBlankModalItem逻辑"""
        logger.info("开始JavaScript方式探索下拉菜单...")

        # 注入JavaScript函数到页面
        self._inject_helper_functions()

        # 获取所有可能的输入元素
        self._get_certain_input_doms()

        # 为每个输入元素获取选项
        self._extract_options_for_all_inputs()

        logger.info(f"探索完成: input_doms={len(self.input_doms)}, select_doms={len(self.select_doms)}")

    def _inject_helper_functions(self):
        """注入JavaScript辅助函数到页面"""
        js_code = """
        // 等待函数
        window.waitTime = function(ms) {
            return new Promise(resolve => setTimeout(resolve, ms || 1000));
        };

        // 点击DOM元素 - 学习arkContent.js的_clickDom
        window._clickDom = async function(element) {
            let targetElement = element;

            if (element.scrollIntoViewIfNeeded) {
                element.scrollIntoViewIfNeeded();
            } else {
                element.scrollIntoView({block: 'center'});
            }

            if (element.offsetWidth > 0 && element.offsetHeight > 0 &&
                window.getComputedStyle(element).visibility !== 'hidden') {
                const rect = element.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                const elementAtPoint = document.elementFromPoint(centerX, centerY);

                if (elementAtPoint && (element.contains(elementAtPoint) ||
                    elementAtPoint.contains(element) ||
                    element.parentNode === elementAtPoint.parentNode)) {
                    targetElement = elementAtPoint;
                }
            }

            // 找到可点击的父元素
            while (targetElement && typeof targetElement.click !== 'function') {
                targetElement = targetElement.parentElement;
            }

            if (!targetElement) targetElement = element;

            // 模拟完整的点击事件序列
            const eventOptions = {bubbles: true, cancelable: true, view: window};

            targetElement.dispatchEvent(new MouseEvent('mousedown', eventOptions));
            targetElement.dispatchEvent(new FocusEvent('focus', eventOptions));
            targetElement.dispatchEvent(new MouseEvent('mouseup', eventOptions));
            targetElement.dispatchEvent(new MouseEvent('click', eventOptions));

            await window.waitTime(10);
        };

        // 检查DOM是否可见
        window._isDomVisible = function(element) {
            if (!element.ownerDocument.contains(element)) return false;

            let current = element;
            while (current) {
                const style = getComputedStyle(current);
                if (style.display === 'none' || style.visibility === 'hidden' ||
                    style.opacity === '0' || current.hidden ||
                    (current.offsetWidth === 0 && current.offsetHeight === 0 && style.overflow === 'hidden')) {
                    return false;
                }
                current = current.parentElement;
            }
            return true;
        };

        // 获取模态框选项
        window._getModalItems = function(container) {
            const items = [];
            const elements = container.querySelectorAll('*');

            for (const element of elements) {
                if (!window._isDomVisible(element)) continue;

                // 检查是否只包含文本节点
                let hasOnlyTextNodes = true;
                for (const child of element.childNodes) {
                    if (child.nodeType !== Node.TEXT_NODE) {
                        hasOnlyTextNodes = false;
                        break;
                    }
                }

                if (hasOnlyTextNodes && element.childNodes.length > 0) {
                    const text = element.textContent.trim();
                    if (text !== '') {
                        items.push(text);
                    }
                }
            }

            return items;
        };

        // 观察DOM变化
        window._observeAddDoms = [];
        window._domObserver = null;

        window._observeDomChanges = function() {
            const body = document.body;
            const addedElements = new WeakSet();

            window._domObserver = new MutationObserver(mutations => {
                for (const mutation of mutations) {
                    if (mutation.type === 'childList') {
                        for (const node of mutation.addedNodes) {
                            if (node.nodeType === Node.ELEMENT_NODE && !addedElements.has(node)) {
                                addedElements.add(node);
                                window._observeAddDoms.push(node);

                                // 添加子元素
                                for (const child of node.querySelectorAll('*')) {
                                    addedElements.add(child);
                                }
                            }
                        }
                    }
                }
            });

            window._domObserver.observe(body, {
                childList: true,
                subtree: true
            });
        };

        window._stopObserveDomChanges = function() {
            if (window._domObserver) {
                window._domObserver.disconnect();
                window._domObserver = null;
            }
        };

        window._clearObserveDoms = function() {
            window._observeAddDoms = [];
        };

        // 检查是否有新的模态框DOM
        window._checkNewModalDom = function() {
            return window._observeAddDoms.length > 0;
        };

        // 处理观察到的模态框
        window._handleObserveModal = function() {
            const newDoms = window._observeAddDoms.filter(dom => {
                let parent = dom.parentElement;
                while (parent) {
                    if (window._observeAddDoms.includes(parent)) {
                        return false;
                    }
                    parent = parent.parentElement;
                }
                return true;
            });

            let modalItems = [];
            for (const dom of newDoms) {
                const items = window._getModalItems(dom);
                if (items.length >= 2) {
                    modalItems = items;
                }
            }

            return modalItems;
        };

        // 关闭模态框
        window._clickCloseModal = async function(element) {
            // 尝试按ESC键
            try {
                element.dispatchEvent(new KeyboardEvent('keydown', {
                    key: 'Escape',
                    keyCode: 27,
                    bubbles: true
                }));
                await window.waitTime(100);
                return;
            } catch (e) {}

            // 尝试点击页面其他位置
            try {
                document.body.click();
                await window.waitTime(100);
                return;
            } catch (e) {}

            // 尝试再次点击元素
            try {
                await window._clickDom(element);
            } catch (e) {}
        };

        console.log('JavaScript helper functions injected successfully');
        """

        self.driver.execute_script(js_code)

    def _get_certain_input_doms(self):
        """获取特定的输入DOM元素 - 学习arkContent.js的_getCertainInputDoms"""
        js_code = """
        const allElements = Array.from(document.body.querySelectorAll('*:not(#ark-ai)'));
        const inputDoms = [];
        const selectDoms = [];
        const radioDoms = [];

        for (const element of allElements) {
            let isInput = false;
            let isSelect = false;
            let isRadio = false;

            // 跳过顶部元素
            if (element.getBoundingClientRect().bottom <= 150) continue;

            const placeholder = element.getAttribute('placeholder');
            const title = element.getAttribute('title');

            // 判断是否为输入元素
            if ((placeholder && element.tagName !== 'TEXTAREA' &&
                 !/^\\s*(搜索|查找)/.test(placeholder) && !/^\\s*(搜索|查找)/.test(title)) ||
                element.classList.contains('ant-select')) {
                isInput = true;
            } else if (element.tagName === 'INPUT' &&
                       !(/^\\s*(搜索|查找)/.test(placeholder) || /^\\s*(搜索|查找)/.test(title)) &&
                       ['text', 'search'].includes(element.type)) {
                isInput = true;
            } else if (element.tagName === 'SELECT') {
                isSelect = true;
            } else if (/(?:^|[^a-zA-Z])radio(?:[^a-zA-Z]|$)/.test(element.className)) {
                isRadio = true;
            }

            if (isInput || isSelect || isRadio) {
                if (window._isDomVisible(element)) {
                    if (isInput) inputDoms.push(element);
                    else if (isSelect) selectDoms.push(element);
                    else radioDoms.push(element);
                } else {
                    // 查找可见的父元素
                    let parent = element.parentElement;
                    while (parent && !window._isDomVisible(parent)) {
                        parent = parent.parentElement;
                    }
                    if (parent) {
                        if (isInput) inputDoms.push(parent);
                        else if (isSelect) selectDoms.push(parent);
                        else radioDoms.push(parent);
                    }
                }
            }
        }

        return {
            inputDoms: inputDoms,
            selectDoms: selectDoms,
            radioDoms: radioDoms
        };
        """

        result = self.driver.execute_script(f"return ({js_code})")

        self.input_doms = result['inputDoms']
        self.select_doms = result['selectDoms']
        self.radio_doms = result['radioDoms']

        logger.info(f"找到DOM元素: input={len(self.input_doms)}, select={len(self.select_doms)}, radio={len(self.radio_doms)}")

    def _extract_options_for_all_inputs(self):
        """为所有输入元素提取选项 - 学习arkContent.js的核心逻辑"""
        logger.info("开始为所有输入元素提取选项...")

        # 处理input类型的元素
        self.input_items = []
        for i, element in enumerate(self.input_doms):
            logger.info(f"处理input元素 {i+1}/{len(self.input_doms)}")
            options = self._extract_options_for_single_element(element)
            self.input_items.append(options)

        # 处理select类型的元素
        self.select_items = []
        for i, element in enumerate(self.select_doms):
            logger.info(f"处理select元素 {i+1}/{len(self.select_doms)}")
            options = self._get_select_items(element)
            self.select_items.append(options)

        # 处理radio类型的元素
        self.radio_items = []
        for i, element in enumerate(self.radio_doms):
            logger.info(f"处理radio元素 {i+1}/{len(self.radio_doms)}")
            options = self._get_modal_items(element)
            self.radio_items.append(options)

    def _extract_options_for_single_element(self, element):
        """为单个元素提取选项 - 学习arkContent.js的核心逻辑"""
        try:
            # 高亮元素
            original_bg = self.driver.execute_script(
                "arguments[0].style.backgroundColor = 'yellow'; return arguments[0].style.backgroundColor;",
                element
            )

            # 滚动到元素
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(0.2)

            # 开始观察DOM变化
            self.driver.execute_script("window._observeDomChanges();")

            # 点击元素
            self.driver.execute_script("window._clickDom(arguments[0]);", element)
            time.sleep(0.1)

            # 检查是否有新的DOM出现
            has_new_modal = self.driver.execute_script("return window._checkNewModalDom();")
            if has_new_modal:
                time.sleep(0.3)

            # 停止观察
            self.driver.execute_script("window._stopObserveDomChanges();")

            # 处理观察到的模态框
            modal_items = self.driver.execute_script("return window._handleObserveModal();")

            # 关闭模态框
            self.driver.execute_script("window._clickCloseModal(arguments[0]);", element)

            # 恢复背景色
            self.driver.execute_script("arguments[0].style.backgroundColor = arguments[1];", element, original_bg)

            # 清理观察的DOM
            self.driver.execute_script("window._clearObserveDoms();")

            # 处理选项
            if modal_items and len(modal_items) > 0:
                # 移除"请选择"类型的第一个选项
                if modal_items and len(modal_items) > 0 and modal_items[0].startswith('请选择'):
                    return modal_items[1:]
                return modal_items

            return []

        except Exception as e:
            logger.warning(f"提取单个元素选项失败: {e}")
            return []

    def _get_select_items(self, element):
        """获取select元素的选项"""
        try:
            js_code = """
            const selectElement = arguments[0];
            const items = [];
            const allElements = selectElement.querySelectorAll('*');

            for (const element of allElements) {
                // 检查是否只包含文本节点
                let hasOnlyTextNodes = true;
                for (const child of element.childNodes) {
                    if (child.nodeType !== Node.TEXT_NODE) {
                        hasOnlyTextNodes = false;
                        break;
                    }
                }

                if (hasOnlyTextNodes && element.childNodes.length > 0) {
                    const text = element.textContent.trim();
                    if (text !== '') {
                        items.push(text);
                    }
                }
            }

            return items;
            """

            items = self.driver.execute_script(js_code, element)

            # 过滤选项
            if items and len(items) >= 2:
                if items[0].startswith('请选择'):
                    return items[1:]
                return items

            return []

        except Exception as e:
            logger.warning(f"获取select选项失败: {e}")
            return []

    def _get_modal_items(self, element):
        """获取模态框选项"""
        try:
            items = self.driver.execute_script("return window._getModalItems(arguments[0]);", element)

            if items and len(items) >= 2:
                return items

            return []

        except Exception as e:
            logger.warning(f"获取模态框选项失败: {e}")
            return []

    def click_select_option(self, element, target_value: str, all_options: List[str]) -> bool:
        """点击选择下拉菜单选项 - JavaScript方式"""
        try:
            logger.info(f"JavaScript方式选择下拉菜单选项: {target_value}")

            # 注入选择逻辑
            js_code = """
            const element = arguments[0];
            const targetValue = arguments[1];
            const allOptions = arguments[2];

            return new Promise(async (resolve) => {
                try {
                    // 滚动到元素
                    element.scrollIntoView({block: 'center'});
                    await window.waitTime(300);

                    // 开始观察DOM变化
                    window._observeDomChanges();

                    // 点击打开下拉菜单
                    await window._clickDom(element);
                    await window.waitTime(500);

                    // 检查是否有新的模态框
                    if (window._checkNewModalDom()) {
                        await window.waitTime(800);

                        // 查找目标选项
                        const newDoms = window._observeAddDoms.filter(dom => {
                            let parent = dom.parentElement;
                            while (parent) {
                                if (window._observeAddDoms.includes(parent)) {
                                    return false;
                                }
                                parent = parent.parentElement;
                            }
                            return true;
                        });

                        let targetElement = null;

                        for (const dom of newDoms) {
                            const elements = dom.querySelectorAll('*');

                            for (const elem of elements) {
                                if (!window._isDomVisible(elem)) continue;

                                // 检查是否只包含文本节点
                                let hasOnlyTextNodes = true;
                                for (const child of elem.childNodes) {
                                    if (child.nodeType !== Node.TEXT_NODE) {
                                        hasOnlyTextNodes = false;
                                        break;
                                    }
                                }

                                if (hasOnlyTextNodes && elem.childNodes.length > 0) {
                                    const text = elem.textContent.trim();

                                    // 精确匹配
                                    if (text === targetValue) {
                                        targetElement = elem;
                                        break;
                                    }

                                    // 模糊匹配
                                    if (!targetElement &&
                                        (text.includes(targetValue) || targetValue.includes(text))) {
                                        targetElement = elem;
                                    }
                                }
                            }

                            if (targetElement) break;
                        }

                        if (targetElement) {
                            // 点击目标选项
                            await window._clickDom(targetElement);
                            await window.waitTime(300);

                            // 验证选择是否生效
                            let success = false;
                            if (element.tagName === 'SELECT') {
                                success = element.value === targetValue || element.selectedOptions[0]?.text === targetValue;
                            } else {
                                success = element.textContent.includes(targetValue) || element.value === targetValue;
                            }

                            window._stopObserveDomChanges();
                            window._clearObserveDoms();

                            resolve(success);
                        } else {
                            console.warn('未找到目标选项:', targetValue);
                            window._stopObserveDomChanges();
                            window._clearObserveDoms();
                            resolve(false);
                        }
                    } else {
                        // 没有模态框，可能是标准select
                        if (element.tagName === 'SELECT') {
                            const options = Array.from(element.options);
                            const targetOption = options.find(opt =>
                                opt.text === targetValue || opt.value === targetValue
                            );

                            if (targetOption) {
                                element.value = targetOption.value;
                                element.dispatchEvent(new Event('change', {bubbles: true}));
                                resolve(true);
                            } else {
                                resolve(false);
                            }
                        } else {
                            resolve(false);
                        }

                        window._stopObserveDomChanges();
                        window._clearObserveDoms();
                    }
                } catch (error) {
                    console.error('选择选项时出错:', error);
                    window._stopObserveDomChanges();
                    window._clearObserveDoms();
                    resolve(false);
                }
            });
            """

            # 执行JavaScript选择逻辑
            success = self.driver.execute_async_script(js_code, element, target_value, all_options)

            if success:
                logger.info(f"✅ JavaScript方式成功选择选项: {target_value}")
            else:
                logger.warning(f"❌ JavaScript方式选择选项失败: {target_value}")

            return success

        except Exception as e:
            logger.error(f"JavaScript方式选择选项异常: {e}")
            return False

    def get_all_dropdown_data(self):
        """获取所有下拉菜单数据"""
        return {
            'input_doms': len(self.input_doms),
            'input_items': self.input_items,
            'select_doms': len(self.select_doms),
            'select_items': self.select_items,
            'radio_doms': len(self.radio_doms),
            'radio_items': self.radio_items
        }
