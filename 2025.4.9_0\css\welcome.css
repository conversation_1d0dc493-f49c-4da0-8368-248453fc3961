:root {
  --page-bg: #0e0e10;
  --primary-bg: #13141f;
  --secondary-bg: #1a1b27;
  --primary-color: #5e61c7;
  --primary-light: #8c7bf9;
  --primary-dark: #494dae;
  --text-primary: #e1e1e6;
  --text-secondary: #8e8e96;
  --border-color: #27272a;
  
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.2);
  
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: var(--page-bg);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1.8;
  padding: 20px;
}

.container {
  width: 100%;
  max-width: 900px;
  margin: 40px auto;
}

.welcome-card {
  background-color: var(--primary-bg);
  border-radius: var(--radius-lg);
  padding: 50px;
  box-shadow: var(--shadow-lg);
}

h1 {
  color: var(--primary-light);
  font-size: 28px;
  margin-bottom: 16px;
  text-align: center;
  font-weight: 600;
}

h2 {
  color: var(--text-primary);
  font-size: 20px;
  margin-bottom: 40px;
  text-align: center;
  font-weight: normal;
}

.content-section {
  margin: 30px 0;
  padding: 24px 28px;
  background-color: var(--secondary-bg);
  border-radius: var(--radius-md);
}

.content-section:first-of-type {
  margin-top: 0;
}

h3 {
  color: var(--primary-light);
  font-size: 20px;
  margin-bottom: 16px;
  font-weight: 600;
}

p {
  color: var(--text-primary);
  margin: 12px 0;
  font-size: 16px;
  line-height: 1.8;
}

ol {
  list-style-position: inside;
  color: var(--text-primary);
  padding-left: 0;
}

li {
  margin: 16px 0;
  padding-left: 24px;
  text-indent: -24px;
  font-size: 16px;
  line-height: 1.8;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: opacity 0.2s;
}

a:hover {
  opacity: 0.8;
}

@media (max-width: 768px) {
  .welcome-card {
    padding: 30px 20px;
  }

  .content-section {
    padding: 20px;
  }

  h1 {
    font-size: 24px;
  }

  h2 {
    font-size: 18px;
  }

  h3 {
    font-size: 18px;
  }

  p, li {
    font-size: 15px;
  }
}

.action-container {
  text-align: center;
  margin-top: 40px;
}

.action-button {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--text-primary);
  border: none;
  padding: 16px 48px;
  font-size: 18px;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: var(--shadow-md);
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
}

.action-button:active {
  transform: translateY(0);
}

.store-link {
  color: var(--primary-light);
  text-decoration: none;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
  font-weight: 500;
}

.store-link:hover {
  background-color: rgba(140, 123, 249, 0.1);
  color: var(--primary-light);
} 