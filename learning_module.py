"""
学习模块 - 记录和学习用户的填写习惯
"""

import os
import json
import logging
from typing import Dict, List, Any
from datetime import datetime
from web_automation import WebAutomation

logger = logging.getLogger(__name__)


class LearningModule:
    """学习模块类"""
    
    def __init__(self, learning_data_path: str = "learning_data.json"):
        self.learning_data_path = learning_data_path
        self.learning_data = self._load_learning_data()
        
    def _load_learning_data(self) -> Dict:
        """加载学习数据"""
        try:
            if os.path.exists(self.learning_data_path):
                with open(self.learning_data_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载学习数据失败: {e}")
        
        return {
            "field_mappings": {},  # 字段映射规则
            "website_patterns": {},  # 网站特定模式
            "user_preferences": {},  # 用户偏好
            "fill_history": []  # 填写历史
        }
        
    def save_learning_data(self):
        """保存学习数据"""
        try:
            with open(self.learning_data_path, 'w', encoding='utf-8') as f:
                json.dump(self.learning_data, f, ensure_ascii=False, indent=2)
            logger.info("学习数据已保存")
        except Exception as e:
            logger.error(f"保存学习数据失败: {e}")
            
    def capture_current_form_state(self, web_automation: WebAutomation) -> Dict[str, Any]:
        """捕获当前表单的填写状态"""
        logger.info("正在捕获当前表单状态...")
        
        form_state = {
            "timestamp": datetime.now().isoformat(),
            "url": web_automation.driver.current_url,
            "title": web_automation.driver.title,
            "fields": []
        }
        
        # 获取所有表单元素
        form_elements = web_automation.get_form_elements()
        
        for element_info in form_elements:
            try:
                element = element_info['element']
                field_data = {
                    "type": element_info['type'],
                    "label": element_info.get('label', ''),
                    "name": element_info.get('name', ''),
                    "placeholder": element_info.get('placeholder', ''),
                    "id": element_info.get('id', ''),
                    "class": element_info.get('class', ''),
                    "current_value": self._get_element_current_value(element, element_info['type']),
                    "original_value": element_info.get('value', '')
                }
                
                # 对于下拉菜单，记录选中的选项
                if element_info['type'] in ['select', 'custom_select']:
                    field_data['options'] = element_info.get('options', [])
                    if element_info['type'] == 'select':
                        try:
                            from selenium.webdriver.support.ui import Select
                            select = Select(element)
                            selected_option = select.first_selected_option
                            field_data['selected_option'] = selected_option.text if selected_option else ''
                        except:
                            pass
                
                form_state["fields"].append(field_data)
                
            except Exception as e:
                logger.debug(f"捕获字段状态失败: {e}")
                continue
                
        logger.info(f"捕获了{len(form_state['fields'])}个字段的状态")
        return form_state
        
    def _get_element_current_value(self, element, element_type: str) -> str:
        """获取元素当前值"""
        try:
            if element_type in ['input', 'textarea']:
                return element.get_attribute('value') or ''
            elif element_type == 'select':
                from selenium.webdriver.support.ui import Select
                select = Select(element)
                selected_option = select.first_selected_option
                return selected_option.text if selected_option else ''
            else:
                # 对于自定义下拉菜单，尝试获取显示文本
                return element.text or element.get_attribute('value') or ''
        except:
            return ''
            
    def learn_from_user_corrections(self, original_suggestions: Dict, final_form_state: Dict):
        """从用户修正中学习"""
        logger.info("开始学习用户修正...")
        
        # 分析用户修改了哪些字段
        corrections = []
        
        for field in final_form_state['fields']:
            field_identifier = self._get_field_identifier(field)
            current_value = field['current_value']
            
            # 检查是否有对应的原始建议
            if field_identifier in original_suggestions:
                original_suggestion = original_suggestions[field_identifier]
                if current_value != original_suggestion.get('value', ''):
                    correction = {
                        'field_identifier': field_identifier,
                        'field_info': field,
                        'ai_suggestion': original_suggestion,
                        'user_correction': current_value,
                        'timestamp': datetime.now().isoformat()
                    }
                    corrections.append(correction)
                    
        logger.info(f"发现{len(corrections)}个用户修正")
        
        # 更新学习数据
        self._update_field_mappings(corrections)
        self._update_user_preferences(corrections)
        
        # 保存填写历史
        self.learning_data['fill_history'].append({
            'timestamp': datetime.now().isoformat(),
            'url': final_form_state['url'],
            'title': final_form_state['title'],
            'corrections_count': len(corrections),
            'total_fields': len(final_form_state['fields'])
        })
        
        self.save_learning_data()
        
    def _get_field_identifier(self, field: Dict) -> str:
        """获取字段标识符"""
        return (field.get('label', '') or 
                field.get('name', '') or 
                field.get('placeholder', '') or
                field.get('id', '') or
                '未知字段')
                
    def _update_field_mappings(self, corrections: List[Dict]):
        """更新字段映射规则"""
        for correction in corrections:
            field_id = correction['field_identifier']
            user_value = correction['user_correction']
            
            if field_id not in self.learning_data['field_mappings']:
                self.learning_data['field_mappings'][field_id] = []
                
            # 记录用户偏好的值
            mapping_entry = {
                'user_value': user_value,
                'field_info': correction['field_info'],
                'frequency': 1,
                'last_used': datetime.now().isoformat()
            }
            
            # 检查是否已存在相同的映射
            existing_mapping = None
            for mapping in self.learning_data['field_mappings'][field_id]:
                if mapping['user_value'] == user_value:
                    existing_mapping = mapping
                    break
                    
            if existing_mapping:
                existing_mapping['frequency'] += 1
                existing_mapping['last_used'] = datetime.now().isoformat()
            else:
                self.learning_data['field_mappings'][field_id].append(mapping_entry)
                
    def _update_user_preferences(self, corrections: List[Dict]):
        """更新用户偏好"""
        for correction in corrections:
            field_type = correction['field_info']['type']
            
            if field_type not in self.learning_data['user_preferences']:
                self.learning_data['user_preferences'][field_type] = {}
                
            # 记录用户在特定类型字段上的偏好
            user_value = correction['user_correction']
            if user_value in self.learning_data['user_preferences'][field_type]:
                self.learning_data['user_preferences'][field_type][user_value] += 1
            else:
                self.learning_data['user_preferences'][field_type][user_value] = 1
                
    def get_learned_suggestion(self, field_info: Dict) -> Dict[str, str]:
        """基于学习数据获取建议"""
        field_id = self._get_field_identifier(field_info)
        
        # 检查是否有学习到的映射
        if field_id in self.learning_data['field_mappings']:
            mappings = self.learning_data['field_mappings'][field_id]
            # 选择使用频率最高的映射
            best_mapping = max(mappings, key=lambda x: x['frequency'])
            
            return {
                'value': best_mapping['user_value'],
                'confidence': 'high',
                'reasoning': f'基于学习数据（使用{best_mapping["frequency"]}次）'
            }
            
        return None
        
    def get_learning_stats(self) -> Dict:
        """获取学习统计信息"""
        total_mappings = sum(len(mappings) for mappings in self.learning_data['field_mappings'].values())
        total_history = len(self.learning_data['fill_history'])
        
        return {
            'total_field_mappings': total_mappings,
            'total_fill_sessions': total_history,
            'learned_fields': len(self.learning_data['field_mappings']),
            'last_learning_time': self.learning_data['fill_history'][-1]['timestamp'] if total_history > 0 else None
        } 