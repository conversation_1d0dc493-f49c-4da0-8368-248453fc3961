<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>欢迎使用 求职方舟AI</title>
  <link rel="stylesheet" href="../css/welcome.css">
</head>
<body>
  <div class="container">
    <div class="welcome-card">
      <h1>恭喜呀！求职方舟AI 插件安装成功！</h1>
      <h2>使用求职方舟，开始你的海投之旅吧~</h2>
      
      <section class="content-section">
        <h3>使用方法：</h3>
        <p>随便进入一个企业的招聘官网，到需要填写简历的时候，页面右下角就会自动出现一个『求职方舟AI』的悬浮按钮，点击按钮就可以启动了。</p>
      </section>

      <section class="content-section">
        <h3>注意事项：</h3>
        <ol>
          <li>如果是从『求职方舟AI』网站的『校招汇总表』跳转的企业官网，会自动同步到『投递记录表』中，强烈建议你去记录和整理你的校招投递信息。</li>
          <li>招聘官网并不限制是从『求职方舟AI』的网站跳转出的企业，任意企业的招聘官网均可使用。</li>
          <li>由于国内的企业官网水平参差不齐，部分公司的网站极不规范，low得惊世骇俗，再加上当前AI的智能水平，以及本插件开发时间有限，简历的填写会出现填写的不全和不好的情况。注意这不是你或你简历的问题，请不要怀疑自己。这些网站真的很难都包容，能包容70%网站的70%字段，我就很满意了，应该就能帮到大家很多了吧。</li>
          <li>剩下没有填写的内容，需要你自己补充，补充的内容，AI会智能学习，或许下次就能填上咯~</li>
        </ol>
      </section>

      <section class="content-section">
        <h3>使用费用：</h3>
        <p>4个字，全站免费。</p>
      </section>

      <section class="content-section">
        <h3>联系我：</h3>
        <p>你可以通过小红书或抖音联系我，搜索『小风学长（offer收割机版）』就可以直接联系我。</p>
      </section>

      <section class="content-section">
        <h3>我的请求：</h3>
        <p>如果你感觉好用，帮我推荐给 同学室友 哈~ 如果可以推给『分管就业的辅导员』就更棒啦！~</p>
        <p>还可以在<a href="https://chromewebstore.google.com/detail/ohjbldefcgdafflncjpnlajjnbkebjap" target="_blank" class="store-link">Chrome商店</a>或<a href="https://microsoftedge.microsoft.com/addons/detail/fhefgbbghlmmedjppiglmfkgeggaikno" target="_blank" class="store-link">Edge商店</a>给本插件一个5星好评哦~</p>
      </section>

      <section class="content-section">
        <h3>最后是祝福：</h3>
        <p>祝你求职顺利~成功上岸~</p>
      </section>

      <div class="action-container">
        <button id="start-delivery-btn" class="action-button">前往投递吧~</button>
      </div>
    </div>
  </div>
  <script src="../js/welcome.js"></script>
</body>
</html> 