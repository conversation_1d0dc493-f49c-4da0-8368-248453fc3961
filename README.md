# 自动简历填写工具 v2.0

一个基于AI视觉识别的自动化简历填写工具，能够根据本地markdown格式的简历自动填写招聘网站的在线表单，并具备智能学习功能。

## 🆕 v2.0 新特性

- 🖼️ **全页面截图**: 捕获整个网页内容，提供更全面的表单识别
- 🧠 **结构化AI输出**: AI返回结构化的建议值，避免过长的无用文本
- 🎓 **智能学习系统**: 记录用户的填写偏好，持续改进填写准确性
- 📊 **学习统计**: 显示学习进度和改进效果

## 功能特点

- 📄 **Markdown简历解析**: 自动解析本地markdown格式的简历文件
- 👁️ **视觉AI识别**: 使用视觉AI模型识别网页表单字段
- 🤖 **智能匹配**: 智能匹配简历内容与表单字段
- 📝 **多类型支持**: 支持文本框、下拉菜单、日期选择器等多种表单类型
- 🔍 **动态内容处理**: 能够处理点击后才出现的动态下拉菜单
- 🎯 **学习适应**: 从用户修正中学习，提高后续填写准确性

## 安装

1. 克隆项目
```bash
git clone <repository-url>
cd cvfiller_llm
```

2. 安装Python依赖
```bash
pip install -r requirements.txt
```

3. 安装浏览器驱动
   - Chrome: 下载 [ChromeDriver](https://chromedriver.chromium.org/)
   - Edge: 下载 [EdgeDriver](https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/)
   - Firefox: 下载 [GeckoDriver](https://github.com/mozilla/geckodriver/releases)

## 配置

1. 编辑 `config.py` 文件，配置API密钥：

```python
API_CONFIG = {
    'openai': {
        'api_key': 'your-openai-api-key',
        # ...
    },
    'qwen': {
        'api_key': 'your-qwen-api-key',
        # ...
    }
}
```

2. 选择使用的API提供商：
```python
ACTIVE_API = 'qwen'  # 可选: 'openai', 'qwen', 'google'
```

3. 配置浏览器类型：
```python
SYSTEM_CONFIG = {
    'browser': 'chrome',  # 可选: 'chrome', 'edge', 'firefox'
    # ...
}
```

## 使用方法

1. 准备简历文件
   - 将您的简历保存为markdown格式
   - 放置在 `resume/markdown.md` 路径下

2. 运行程序
```bash
python main.py
```

3. 按照提示操作
   - 程序会自动打开浏览器
   - 手动登录招聘网站
   - 进入简历填写页面
   - 按回车键开始自动填写

4. 🆕 学习阶段
   - 填写完成后，检查并修改不准确的内容
   - 按回车键，系统会学习您的修改偏好
   - 下次填写时会优先使用学习到的值

## 学习功能说明

### 学习机制
- 系统会记录您对AI建议的修改
- 建立字段类型与偏好值的映射关系
- 统计使用频率，优先选择高频值
- 支持网站特定的填写模式学习

### 学习数据存储
- 学习数据保存在 `learning_data.json` 文件中
- 包含字段映射、用户偏好、填写历史等信息
- 可以手动备份和恢复学习数据

### 隐私保护
- 所有学习数据仅保存在本地
- 不会上传任何个人信息到云端
- 可以随时删除学习数据文件重新开始

## 简历格式示例

```markdown
# 姓名

电话: xxx-xxxx-xxxx 邮箱: <EMAIL>

# 教育经历

# 大学名称
专业名称
2020年09月 - 2024年06月
GPA: 3.8/4.0

# 工作经历

# 公司名称
职位名称
2023年07月 - 2024年09月
- 工作职责1
- 工作职责2

# 技能

编程语言: Python, Java, JavaScript
工具: Git, Docker, Kubernetes
```

## 注意事项

1. **API密钥安全**: 请勿将包含真实API密钥的配置文件提交到版本控制系统
2. **网站兼容性**: 不同招聘网站的表单结构可能不同，程序会尽力适配
3. **人工检查**: 自动填写完成后，请务必人工检查填写结果的准确性
4. **合理使用**: 请遵守各招聘网站的使用条款
5. **学习数据**: 定期备份 `learning_data.json` 文件以保护学习成果

## 故障排除

1. **浏览器驱动错误**: 确保浏览器驱动版本与浏览器版本匹配
2. **API调用失败**: 检查API密钥是否正确，网络连接是否正常
3. **元素定位失败**: 某些网站可能使用了反爬虫技术，可能需要手动处理
4. **学习功能异常**: 检查是否有写入权限，learning_data.json文件是否损坏

## 开发计划

- [ ] 支持更多简历格式（PDF、Word等）
- [ ] 增加更多AI模型支持
- [ ] 优化表单字段识别准确率
- [ ] 添加批量投递功能
- [ ] 支持更多语言
- [x] 智能学习系统
- [x] 全页面截图支持
- [x] 结构化AI输出

## 更新日志

### v2.0 (当前版本)
- 新增智能学习功能
- 支持全页面截图识别
- 优化AI输出格式
- 改进用户交互体验
- 增加学习统计功能

### v1.0
- 基础的表单自动填写功能
- 支持多种表单类型
- 集成视觉AI识别

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 许可证

MIT License 